import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { EntityRelationalHelper } from '@utils/relational-entity-helper';
import { AutoMap } from '@automapper/classes';
import { UserEntity } from '../../../../user/users/infrastructure/entities/user.entity';

@Entity('addresses')
export class AddressEntity extends EntityRelationalHelper {
  @AutoMap()
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @AutoMap()
  @Column('uuid')
  @Index()
  tenantId: string;

  @AutoMap()
  @ManyToOne(() => UserEntity)
  @JoinColumn({ name: 'customerId' })
  @Index()
  customer: UserEntity;

  @AutoMap()
  @Column('uuid')
  customerId: string;

  @AutoMap()
  @Column()
  name: string;

  @AutoMap()
  @Column()
  companyName: string;

  @AutoMap()
  @Column()
  email: string;

  @AutoMap()
  @Column()
  countryCode: string;

  @AutoMap()
  @Column()
  phoneNumber: string;

  @AutoMap()
  @Column({ nullable: true })
  phoneExtension: number;

  @AutoMap()
  @Column()
  addressLine1: string;

  @AutoMap()
  @Column({ nullable: true })
  addressLine2: string;

  @AutoMap()
  @Column()
  city: string;

  @AutoMap()
  @Column()
  province: string;

  @AutoMap()
  @Column()
  postalCode: string;

  @AutoMap()
  @Column()
  country: string;

  @AutoMap()
  @Column({ type: 'text', nullable: true })
  notes: string;

  @AutoMap()
  @Column({ type: 'float', nullable: true })
  latitude: number;

  @AutoMap()
  @Column({ type: 'float', nullable: true })
  longitude: number;

  @AutoMap()
  @Column({ default: false })
  isDeleted: boolean;

  @AutoMap()
  @DeleteDateColumn({ type: 'timestamptz', nullable: true })
  deletedAt: Date;

  @AutoMap()
  @CreateDateColumn({ type: 'timestamptz', default: () => 'CURRENT_TIMESTAMP' })
  createdAt: Date;

  @AutoMap()
  @UpdateDateColumn({ type: 'timestamptz', default: () => 'CURRENT_TIMESTAMP' })
  updatedAt: Date;

  @AutoMap()
  @Column({ type: 'uuid', nullable: true })
  createdBy: string;

  @AutoMap()
  @Column({ type: 'uuid', nullable: true })
  updatedBy: string;
}
