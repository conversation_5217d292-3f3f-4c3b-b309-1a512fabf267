import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { FindOptionsWhere, Repository } from 'typeorm';
import { Mapper } from '@automapper/core';
import { InjectMapper } from '@automapper/nestjs';
import { EntityCondition } from '@utils/types/entity-condition.type';
import { NullableType } from '@utils/types/nullable.type';
import { AddressEntity } from '../entities/address.entity';
import { AddressDomain } from '../../domain/address';
import { UserEntity } from '../../../../user/users/infrastructure/entities/user.entity';
import { UserNotFoundException } from '../../../../../utils/errors/exceptions/user.exceptions';
import { BaseFilterDto } from '../../../../../core/infrastructure/filtering/dtos/base-filter.dto';
import { PaginatedResult } from '../../../../../utils/query-creator/interfaces';
import { SecureFilterService } from '../../../../../core/infrastructure/filtering/services/secure-filter.service';
import { AddressFilterConfig } from '../../address-filter.config';

@Injectable()
export class AddressRepository {
  constructor(
    @InjectRepository(AddressEntity)
    private readonly addressRepository: Repository<AddressEntity>,
    @InjectRepository(UserEntity)
    private readonly userRepository: Repository<UserEntity>,
    private readonly filterService: SecureFilterService,
    @InjectMapper() private readonly mapper: Mapper,
  ) {
    this.filterService = new SecureFilterService(AddressFilterConfig());
  }

  async create(data: AddressDomain): Promise<AddressDomain> {
    const requestEntity = this.mapper.map(data, AddressDomain, AddressEntity);
    const user = await this.userRepository.findOne({
      where: { id: requestEntity.customerId },
    });
    if (!user) {
      throw new UserNotFoundException(requestEntity.customerId);
    }

    const addressEntity = await this.addressRepository.save(
      this.addressRepository.create(requestEntity),
    );
    const responseDomain = this.mapper.map(
      addressEntity,
      AddressEntity,
      AddressDomain,
    );
    return responseDomain;
  }

  async find(
    filter: BaseFilterDto,
    tenantId: string,
    customerId?: string,
  ): Promise<PaginatedResult<AddressDomain>> {
    const queryBuilder = this.addressRepository
      .createQueryBuilder('address')
      .where('address.isDeleted = false')
      .andWhere('address.tenantId = :tenantId', { tenantId })
      .leftJoinAndSelect('address.customer', 'customer');

    if (customerId) {
      const user = await this.userRepository.findOne({
        where: { id: customerId },
      });
      if (!user) {
        throw new UserNotFoundException(customerId);
      }
      queryBuilder.andWhere('address.customerId = :customerId', { customerId });
    }

    await this.filterService.buildQuery(queryBuilder, filter);
    const result = await this.filterService.executeQuery(queryBuilder, filter);

    const mappedData = this.mapper.mapArray(
      result.data,
      AddressEntity,
      AddressDomain,
    );

    return {
      ...result,
      data: mappedData,
    };
  }

  async findOne(
    fields: EntityCondition<AddressDomain>,
  ): Promise<NullableType<AddressDomain>> {
    const requestEntity: Partial<AddressEntity> = this.mapper.map(
      fields,
      AddressDomain,
      AddressEntity,
    );
    const addressEntity = await this.addressRepository.findOne({
      where: {
        ...(requestEntity as FindOptionsWhere<AddressEntity>),
        isDeleted: false,
      },
      relations: ['customer'],
    });
    if (addressEntity) {
      const responseDomain = this.mapper.map(
        addressEntity,
        AddressEntity,
        AddressDomain,
      );
      return responseDomain;
    }
    return null;
  }

  async update(payload: AddressDomain): Promise<AddressDomain> {
    const requestEntity = this.mapper.map(
      payload,
      AddressDomain,
      AddressEntity,
    );
    const addressEntity = await this.addressRepository.save(requestEntity);
    const responseDomain = this.mapper.map(
      addressEntity,
      AddressEntity,
      AddressDomain,
    );
    return responseDomain;
  }


}
