import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AddressRepository } from './repositories/address.repository';
import { AddressEntity } from './entities/address.entity';
import { UserEntity } from '../../../user/users/infrastructure/entities/user.entity';
import { SecureFilterService } from '../../../../core/infrastructure/filtering/services/secure-filter.service';
import { AddressFilterConfig } from '../address-filter.config';
import { ContactAddressPreferenceEntity } from './entities/contact-address-preferences.entity';
import { ContactEntity } from '../../../user/contacts/infrastructure/persistence/relational/entities/contact.entity';
import { ContactAddressPreferenceRepository } from './repositories/contact-address-preferences.repository';

@Module({
  imports: [TypeOrmModule.forFeature([AddressEntity, UserEntity, ContactAddressPreferenceEntity, ContactEntity])],
  providers: [
    AddressRepository,
    ContactAddressPreferenceRepository,
    {
      provide: SecureFilterService,
      useFactory: () => new SecureFilterService(AddressFilterConfig()),
    },
  ],
  exports: [AddressRepository, ContactAddressPreferenceRepository],
})
export class RelationalAddressPersistenceModule { }
