import { Injectable } from '@nestjs/common';
import { GetAllVehiclesForMobileDto } from '../../mobile/vehicle/dto/get-vehicle.dto';
import { VehicleRepository } from '../../vehicle/vehicles/infrastructure/repositories/vehicle.repository';
import { Mapper } from '@automapper/core';
import { InjectMapper } from '@automapper/nestjs';
import { VehicleEntity } from '../../vehicle/vehicles/infrastructure/entities/vehicle.entity';

@Injectable()
export class VehiclesMobileService {
  constructor(
    private readonly vehicleRepository: VehicleRepository,
    @InjectMapper() private readonly mapper: Mapper,
  ) {}

  async getAllVehiclesMinimal(
    tenantId: string,
  ): Promise<GetAllVehiclesForMobileDto[]> {
    const vehicles = await this.vehicleRepository.findAllMinimal(tenantId);

    const mappedData = this.mapper.mapArray(
      vehicles,
      VehicleEntity,
      GetAllVehiclesForMobileDto,
    );
    return mappedData;
  }
}
