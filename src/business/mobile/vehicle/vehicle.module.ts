import { Module } from '@nestjs/common';
import { MobileAuthModule } from '../auth/auth.module';
import { MobileVehiclesController } from './vehicles.controller';
import { VehiclesService } from '../../vehicle/vehicles/vehicles.service';
import { RelationalVehiclePersistenceModule } from '../../vehicle/vehicles/infrastructure/relational-persistence.module';
import { RelationalVehicleTypePersistenceModule } from '../../vehicle/vehicle-types/infrastructure/relational-persistence.module';
import { RelationalUserPersistenceModule } from '../../user/users/infrastructure/relational-persistence.module';

@Module({
  imports: [
    MobileAuthModule,
    RelationalVehiclePersistenceModule,
    RelationalVehicleTypePersistenceModule,
    RelationalUserPersistenceModule,
  ],
  controllers: [MobileVehiclesController],
  providers: [VehiclesService],
  exports: [],
})
export class MobileVehicleModule {}
