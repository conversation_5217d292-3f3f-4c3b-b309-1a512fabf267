import {
  ForbiddenException,
  HttpStatus,
  Inject,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import {
  ANALYTICS_SERVICE,
  IAnalyticsService,
} from '@core/analytics/analytics.interface';
import { ContactRepository } from './infrastructure/persistence/contact.repository';
import { Contact } from './domain/contact';
import { CreateContactDto } from './dto/create-contact.dto';
import { UpdateContactDto } from './dto/update-contact.dto';
import {
  BaseQueryParams,
  PaginatedResult,
} from '@utils/query-creator/interfaces';
import { CustomerCategoriesService } from '../customer-categories/customer-categories.service';
import { hash } from 'bcrypt';
import {
  ContactPermissions,
  DEFAULT_CONTACT_PERMISSIONS,
} from './domain/contact-permissions';
import { UpdateContactPermissionsDto } from '@app/business/user/contacts/dto/update-contact-permissions.dto';
import { DataSource, SelectQueryBuilder } from 'typeorm';
import { AppException } from '@utils/errors/app.exception';
import { ErrorCode } from '@utils/errors/error-codes';
import {
  ContactAlreadyExistsException,
  ContactNotFoundException,
} from '@utils/errors/exceptions/contact.exceptions';
import { ContactEntity } from '@app/business/user/contacts/infrastructure/persistence/relational/entities/contact.entity';
import { UserEntity } from '@app/business/user/users/infrastructure/entities/user.entity';
import { SecureFilterService } from '@core/infrastructure/filtering/services/secure-filter.service';
import { ParsedQs } from 'qs';
import { BaseFilterDto } from '../../../core/infrastructure/filtering/dtos/base-filter.dto';

@Injectable()
export class ContactsService {
  constructor(
    private readonly contactRepository: ContactRepository,
    private readonly customerCategoryService: CustomerCategoriesService,
    private readonly connection: DataSource,
    private readonly secureFilterService: SecureFilterService,
    @Inject(ANALYTICS_SERVICE)
    private readonly analyticsService: IAnalyticsService,
  ) { }

  /**
   * Creates a base query builder for contact entities
   * Used by the filter endpoint to build filtered queries
   */
  createQueryBuilder(customerId: string): SelectQueryBuilder<ContactEntity> {
    return this.connection
      .getRepository(ContactEntity)
      .createQueryBuilder('contact')
      .where('contact.userId = :customerId', { customerId })
      .andWhere('contact.isDeleted = :isDeleted', { isDeleted: false });
  }

  /**
   * Filter contacts using the key-operator-value filtering system
   * @param customerId The customer ID to filter contacts for
   * @param query
   * @param filter
   * @returns Paginated result of filtered contacts
   */
  async filter(
    customerId: string,
    query: ParsedQs,
    filter: BaseFilterDto,
  ): Promise<PaginatedResult<Contact>> {
    try {
      // Parse the query parameters into a structured filter
      const combinedFilter =
        this.secureFilterService.parseKeyOperatorValueQuery(query, filter);

      // Create a base query for this customer's contacts
      const queryBuilder = this.createQueryBuilder(customerId);

      // Apply the filter to the query
      await this.secureFilterService.buildQuery(queryBuilder, combinedFilter);

      // Execute the query and get paginated results
      const result = await this.secureFilterService.executeQuery(
        queryBuilder,
        combinedFilter,
      );

      // Collect all unique category IDs from all contacts
      const allCategoryIds = new Set<string>();
      result.data.forEach((entity) => {
        if (entity.categoryIds && Array.isArray(entity.categoryIds)) {
          entity.categoryIds.forEach((id) => allCategoryIds.add(id));
        }
      });

      // If we have any category IDs, fetch them in a single query
      const categoriesMap: Map<string, any> = new Map();
      if (allCategoryIds.size > 0) {
        const categories = await this.customerCategoryService.findByIds(
          Array.from(allCategoryIds),
        );

        // Create a map for quick lookup
        categories.forEach((category) => {
          if (category.id != null) {
            categoriesMap.set(category.id, category);
          }
        });
      }

      // Map the entities to domain objects
      const mappedData = result.data.map((entity) => {
        const domain = new Contact();
        domain.id = entity.id;
        domain.name = entity.name;
        domain.email = entity.email;
        domain.phoneNumber = entity.phoneNumber;
        domain.phoneCountryCode = entity.phoneCountryCode;
        domain.phoneExtension = entity.phoneExtension;
        domain.permissions = entity.permissions;
        domain.isActive = entity.isActive;
        domain.createdAt = entity.createdAt;
        domain.updatedAt = entity.updatedAt;
        domain.metadata = entity.metadata;
        domain.isPrimary = entity.isPrimary;

        domain.categories = [];
        if (entity.categoryIds && Array.isArray(entity.categoryIds)) {
          domain.categories = entity.categoryIds
            .map((id) => {
              const category = categoriesMap.get(id);
              if (category) {
                return {
                  id: category.id,
                  name: category.name,
                };
              }
              return null;
            })
            .filter(Boolean);
        }

        return domain;
      });

      return {
        ...result,
        data: mappedData,
      };
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        `Failed to filter contacts: ${error.message}`,
        ErrorCode.VALIDATION_ERROR,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async create(
    tenantId: string,
    userId: string,
    createContactDto: CreateContactDto,
  ): Promise<Contact> {
    const queryRunner = this.connection.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const existingContact = await this.findByEmail(createContactDto.email);

      if (existingContact) {
        throw new ContactAlreadyExistsException(createContactDto.email, userId);
      }
      const { categories } = createContactDto;
      const temporaryPassword = 'password123';

      const hashedPassword = await hash(temporaryPassword, 10);
      let categoryIds: string[] = [];
      if (categories && categories.length > 0) {
        categoryIds = await this.customerCategoryService.processCategories(
          tenantId,
          categories,
          true,
        );
      }
      console.log({ createContactDto });
      const contactEntity = queryRunner.manager.create(ContactEntity, {
        userId,
        name: createContactDto.name,
        email: createContactDto.email,
        phoneExtension: createContactDto.phoneExtension,
        phoneNumber: createContactDto.phoneNumber,
        phoneCountryCode: createContactDto.phoneCountryCode,
        metadata: createContactDto.metadata || {},
        isActive: createContactDto.isActive,
        password: hashedPassword,
        emailVerified: false,
        permissions: createContactDto.permissions,
        categoryIds: categoryIds,
        loginCount: 0,
        failedAttempts: 0,
      });
      const savedContactEntity = await queryRunner.manager.save(contactEntity);
      await queryRunner.commitTransaction();

      // Track contact creation event
      await this.analyticsService.trackEvent(
        savedContactEntity.id,
        'contact_created',
        {
          userId,
          email: savedContactEntity.email,
          name: savedContactEntity.name,
          timestamp: new Date().toISOString(),
        },
      );

      const response = new Contact();
      response.id = savedContactEntity.id;
      response.name = savedContactEntity.name;
      response.email = savedContactEntity.email;
      response.phoneExtension = savedContactEntity.phoneExtension;
      response.phoneNumber = savedContactEntity.phoneNumber;
      response.phoneCountryCode = savedContactEntity.phoneCountryCode;
      response.permissions = savedContactEntity.permissions;
      response.isActive = savedContactEntity.isActive;

      return response;
    } catch (error) {
      await queryRunner.rollbackTransaction();
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        `Failed to create customer contact: ${error.message}`,
        ErrorCode.USER_ALREADY_EXISTS,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    } finally {
      await queryRunner.release();
    }
  }

  async findByEmail(email: string): Promise<Contact | null> {
    return await this.contactRepository.findByEmail(email);
  }

  async findAll(
    userId: string,
    queryParams?: BaseQueryParams,
  ): Promise<PaginatedResult<Contact>> {
    return this.contactRepository.findAll(userId, queryParams);
  }

  async findById(id: string): Promise<Contact> {
    const contact = await this.contactRepository.findById(id);
    if (!contact) {
      throw new NotFoundException(`Contact with ID ${id} not found`);
    }

    // Get the user (customer) to retrieve the tenant ID
    const user = await this.connection
      .getRepository(UserEntity)
      .findOne({ where: { id: contact.userId } });

    if (user) {
      // Add the tenant ID to the contact object
      contact['tenantId'] = user.tenantId;
    }

    console.log('contact in find by id : ', contact);
    return contact;
  }

  async update(
    id: string,
    userId: string,
    tenantId: string,
    updateContactDto: UpdateContactDto,
  ): Promise<Contact> {
    const existingContact = await this.findById(id);

    if (!existingContact) {
      throw new ContactNotFoundException(id);
    }

    if (
      updateContactDto.email &&
      updateContactDto.email !== existingContact.email
    ) {
      const userWithSameEmail = await this.findByEmail(updateContactDto.email);
      if (userWithSameEmail && userWithSameEmail.id !== userId) {
        throw new ContactAlreadyExistsException(updateContactDto.email, id);
      }
    }

    try {
      const { categories, ...customerDataWithoutCategories } = updateContactDto;
      let categoryIds: string[] = [];
      if (categories && categories.length > 0) {
        categoryIds = await this.customerCategoryService.processCategories(
          tenantId,
          categories,
          true,
        );
      }
      const updatedContactData = {
        ...existingContact,
        ...customerDataWithoutCategories,
        categoryIds,
        userId,
      };

      const savedContact = await this.connection
        .getRepository(ContactEntity)
        .save(updatedContactData);

      const response = new Contact();
      response.id = savedContact.id;
      response.name = savedContact.name;
      response.email = savedContact.email;
      response.phoneExtension = savedContact.phoneExtension;
      response.phoneNumber = savedContact.phoneNumber;
      response.phoneCountryCode = savedContact.phoneCountryCode;
      response.permissions = savedContact.permissions;

      return response;
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        `Failed to update contact: ${error.message}`,
        ErrorCode.USER_OPERATION_NOT_ALLOWED,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async delete(id: string, userId: string): Promise<void> {
    const contact = await this.findById(id);

    // Ensure this contact belongs to the user
    if (contact.userId !== userId) {
      throw new ForbiddenException('Contact does not belong to this customer');
    }

    await this.contactRepository.softDelete(id);
  }

  async restore(id: string, userId: string): Promise<void> {
    const contact = await this.contactRepository.findById(id);

    if (contact && contact.userId !== userId) {
      throw new ForbiddenException('Contact does not belong to this customer');
    }

    await this.contactRepository.restore(id);
  }

  async resetPassword(
    id: string,
    userId: string,
  ): Promise<{ newPassword: string }> {
    const contact = await this.findById(id);

    // Ensure this contact belongs to the user
    if (contact.userId !== userId) {
      throw new ForbiddenException('Contact does not belong to this customer');
    }

    // Generate a new password
    const newPassword =
      Math.random().toString(36).slice(-8) +
      Math.random().toString(36).slice(-8);
    const hashedPassword = await hash(newPassword, 10);

    // Update the contact with the new password
    await this.contactRepository.update(id, {
      password: hashedPassword,
      emailVerified: false, // Reset verification status
    });

    return { newPassword };
  }

  async updatePermissions(
    id: string,
    userId: string,
    permissions: UpdateContactPermissionsDto,
  ): Promise<Contact> {
    const contact = await this.findById(id);

    // Ensure this contact belongs to the user
    if (contact.userId !== userId) {
      throw new ForbiddenException('Contact does not belong to this customer');
    }

    // Start with existing permissions or defaults
    const currentPermissions = contact.permissions || {
      ...DEFAULT_CONTACT_PERMISSIONS,
    };

    // Update specific fields only if they are provided as boolean values
    const updatedPermissions: ContactPermissions = {
      address:
        typeof permissions.address === 'boolean'
          ? permissions.address
          : currentPermissions.address,
      prices:
        typeof permissions.prices === 'boolean'
          ? permissions.prices
          : currentPermissions.prices,
      invoices:
        typeof permissions.invoices === 'boolean'
          ? permissions.invoices
          : currentPermissions.invoices,
    };

    return this.contactRepository.update(id, {
      permissions: updatedPermissions,
    });
  }

  async checkPermission(
    contactId: string,
    permission: keyof ContactPermissions,
  ): Promise<boolean> {
    const contact = await this.findById(contactId);

    if (!contact.permissions) {
      return false;
    }

    return !!contact.permissions[permission];
  }
}
