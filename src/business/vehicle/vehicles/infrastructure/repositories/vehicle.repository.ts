import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';

import { FindOptionsWhere, Repository } from 'typeorm';

import { VehicleEntity } from '../entities/vehicle.entity';
import { VehicleDomain } from '../../domain/vehicle';
import { Mapper } from '@automapper/core';
import { InjectMapper } from '@automapper/nestjs';
import { EntityCondition } from '@utils/types/entity-condition.type';
import { NullableType } from '@utils/types/nullable.type';
import { SecureFilterService } from '@core/infrastructure/filtering/services/secure-filter.service';
import { VehicleFilterConfig } from '../../vehicle-filter.config';
import { BaseFilterDto } from '@core/infrastructure/filtering/dtos/base-filter.dto';
import { PaginatedResult } from '@utils/query-creator/interfaces';

@Injectable()
export class VehicleRepository {
  constructor(
    @InjectRepository(VehicleEntity)
    private readonly vehicleRepository: Repository<VehicleEntity>,
    private readonly filterService: SecureFilterService,
    @InjectMapper() private readonly mapper: Mapper,
  ) {
    this.filterService = new SecureFilterService(VehicleFilterConfig());
  }

  async create(data: VehicleDomain): Promise<VehicleDomain> {
    const requestEntity = this.mapper.map(data, VehicleDomain, VehicleEntity);
    const vehicleEntity = await this.vehicleRepository.save(
      this.vehicleRepository.create(requestEntity),
    );
    const responseDomain = this.mapper.map(
      vehicleEntity,
      VehicleEntity,
      VehicleDomain,
    );
    return responseDomain;
  }
  async findAllMinimal(tenantId: string): Promise<VehicleEntity[]> {
    const vehicles = await this.vehicleRepository.find({
      where: {
        tenantId,
        isAvailable: true,
        isDeleted: false,
      },
      relations: ['vehicleType'],
      select: ['id', 'vehicleType', 'model', 'currentOdometer'],
      order: { createdAt: 'DESC' },
    });

    return vehicles;
  }

  async find(
    filter: BaseFilterDto,
    tenantId: string,
  ): Promise<PaginatedResult<VehicleDomain>> {
    const queryBuilder = this.vehicleRepository
      .createQueryBuilder('vehicle')
      .leftJoinAndSelect('vehicle.vehicleType', 'vehicleType')
      .where('vehicle.isDeleted = false')
      .andWhere('vehicle.tenantId = :tenantId', { tenantId });

    await this.filterService.buildQuery(queryBuilder, filter);
    const result = await this.filterService.executeQuery(queryBuilder, filter);

    const mappedData = this.mapper.mapArray(
      result.data,
      VehicleEntity,
      VehicleDomain,
    );

    return {
      ...result,
      data: mappedData,
    };
  }

  async findOne(
    fields: EntityCondition<VehicleDomain>,
  ): Promise<NullableType<VehicleDomain>> {
    const requestEntity: Partial<VehicleEntity> = this.mapper.map(
      fields,
      VehicleDomain,
      VehicleEntity,
    );
    const vehicleEntity = await this.vehicleRepository.findOne({
      where: {
        ...(requestEntity as FindOptionsWhere<VehicleEntity>),
        isDeleted: false,
      },
      relations: ['vehicleType'],
    });
    if (vehicleEntity) {
      const responseDomain = this.mapper.map(
        vehicleEntity,
        VehicleEntity,
        VehicleDomain,
      );
      return responseDomain;
    }
    return null;
  }

  async update(payload: VehicleDomain): Promise<VehicleDomain> {
    const requestEntity = this.mapper.map(
      payload,
      VehicleDomain,
      VehicleEntity,
    );
    const vehicleEntity = await this.vehicleRepository.save(requestEntity);
    const responseDomain = this.mapper.map(
      vehicleEntity,
      VehicleEntity,
      VehicleDomain,
    );
    return responseDomain;
  }

  async countByType(vehicleTypeId: string): Promise<number> {
    const count = await this.vehicleRepository.count({
      where: {
        vehicleTypeId,
        isDeleted: false,
      },
    });
    return count;
  }
}
