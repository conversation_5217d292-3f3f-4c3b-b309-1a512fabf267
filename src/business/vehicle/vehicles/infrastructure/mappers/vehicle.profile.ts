import { createMap, forMember, mapFrom, Mapper } from '@automapper/core';
import { AutomapperProfile, InjectMapper } from '@automapper/nestjs';
import { Injectable } from '@nestjs/common';
import { VehicleDomain } from '../../domain/vehicle';
import { CreateVehicleDto } from '../../dto/create-vehicle.dto';
import { VehicleEntity } from '../entities/vehicle.entity';
import { GetVehicleDto } from '../../dto/get-vehicle.dto';
import { GetAllVehiclesForMobileDto } from '../../../../mobile/vehicle/dto/get-vehicle.dto';

@Injectable()
export class VehicleProfile extends AutomapperProfile {
  constructor(@InjectMapper() mapper: Mapper) {
    super(mapper);
  }

  override get profile() {
    return (mapper: Mapper) => {
      createMap(
        mapper,
        CreateVehicleDto,
        VehicleDomain,
        forMember(
          (dest) => dest.packageType,
          mapFrom((src) => src.packageType),
        ),
      );

      createMap(
        mapper,
        VehicleDomain,
        VehicleEntity,
        forMember(
          (dest) => dest.packageType,
          mapFrom((src) => src.packageType),
        ),
      );

      createMap(
        mapper,
        VehicleEntity,
        VehicleDomain,
        forMember(
          (dest) => dest.packageType,
          mapFrom((src) => src.packageType),
        ),
      );

      createMap(
        mapper,
        VehicleDomain,
        GetVehicleDto,
        forMember(
          (dest) => dest.packageType,
          mapFrom((src) => src.packageType),
        ),
        forMember(
          (dest) => dest.vehicleType,
          mapFrom((src) => src.vehicleType?.name),
        ),
      );
      createMap(
        mapper,
        VehicleEntity,
        GetAllVehiclesForMobileDto,
        forMember(
          (dest) => dest.vehicleType,
          mapFrom((src) => src.vehicleType?.name),
        ),
      );
    };
  }
}
