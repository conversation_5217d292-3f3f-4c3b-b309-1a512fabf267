import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  HttpStatus,
  Param,
  Post,
  Put,
  Query,
  Req,
  UseGuards,
} from '@nestjs/common';
import {
  ApiCookieAuth,
  ApiCreatedResponse,
  ApiNoContentResponse,
  ApiOkResponse,
  ApiOperation,
  ApiTags,
} from '@nestjs/swagger';
import { VehiclesService } from './vehicles.service';
import { Mapper } from '@automapper/core';
import { InjectMapper } from '@automapper/nestjs';
import { CreateVehicleDto } from './dto/create-vehicle.dto';
import { VehicleDomain } from './domain/vehicle';
import { GetAllVehicleDto, GetVehicleDto } from './dto/get-vehicle.dto';
import { JwtAuthGuard } from '../../../core/auth/guards/jwt-auth.guard';
import { TenantAuthGuard } from '../../../core/auth/guards/tenant-auth.guard';
import { RequestWithUser } from '../../../core/auth/interceptors/tenant-context.interceptor';
import { VehicleOperationNotAllowedException } from '../../../utils/errors/exceptions/vehicle-exceptions';
import { SecureFilterService } from '../../../core/infrastructure/filtering/services/secure-filter.service';
import { GenerateFleetIdDto } from './dto/generate-fleet-id.dto';
import { BaseFilterDto } from '../../../core/infrastructure/filtering/dtos/base-filter.dto';
import {
  PermissionGuard,
  RequirePermission,
} from '../../../core/rbac/decorators/require-permission.decorator';

@ApiTags('Business - Vehicle - Vehicles')
@Controller({
  path: 'vehicles',
  version: '1',
})
@ApiCookieAuth()
@UseGuards(JwtAuthGuard, TenantAuthGuard, PermissionGuard)
export class VehiclesController {
  constructor(
    private readonly vehiclesService: VehiclesService,
    private readonly secureFilterService: SecureFilterService,
    @InjectMapper() private readonly mapper: Mapper,
  ) {}

  @Post()
  @ApiOperation({ summary: 'Create new Vehicle' })
  @HttpCode(HttpStatus.CREATED)
  @ApiCreatedResponse({ description: 'Created' })
  async create(
    @Req() request: RequestWithUser,
    @Body() createVehicleDto: CreateVehicleDto,
  ): Promise<VehicleDomain> {
    const tenantId = request.tenantContext?.tenantId;
    if (!tenantId) {
      throw new VehicleOperationNotAllowedException(
        request.user?.id || 'unknown',
        'create',
        'Insufficient tenant access permissions',
      );
    }
    try {
      const vehicleDomain = this.mapper.map(
        createVehicleDto,
        CreateVehicleDto,
        VehicleDomain,
      );
      vehicleDomain.tenantId = tenantId;
      const vehicle = await this.vehiclesService.create(vehicleDomain);
      return vehicle;
    } catch (error) {
      throw error;
    }
  }

  @Get()
  @RequirePermission('create_customer')
  @ApiOperation({
    summary: 'Get all vehicles with pagination and advanced filtering',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({ description: 'OK', type: GetAllVehicleDto })
  async getVehicleList(
    @Req() request: RequestWithUser,
    @Query() filter: BaseFilterDto,
  ): Promise<GetAllVehicleDto> {
    try {
      const combinedFilter =
        this.secureFilterService.parseKeyOperatorValueQuery(
          request.query,
          filter,
        );

      const tenantId = request.tenantContext?.tenantId;
      if (!tenantId) {
        throw new VehicleOperationNotAllowedException(
          request.user?.id || 'unknown',
          'getVehicleList',
          'Insufficient tenant access permissions',
        );
      }

      const result = await this.vehiclesService.getVehicleList(
        combinedFilter,
        tenantId,
      );

      const mappedData = this.mapper.mapArray(
        result.data,
        VehicleDomain,
        GetVehicleDto,
      );

      const response: GetAllVehicleDto = {
        total: result.total,
        page: result.page,
        limit: result.limit,
        totalPages: result.totalPages,
        hasNextPage: result.hasNextPage,
        hasPreviousPage: result.hasPreviousPage,
        data: mappedData,
      };
      return response;
    } catch (error) {
      throw error;
    }
  }

  @Get('/minimal')
  @ApiOperation({
    summary: 'Get all Vehicles (no pagination)',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({ description: 'OK', type: GetVehicleDto })
  async getAllVehiclesMinimal(@Req() request: RequestWithUser) {
    try {
      const tenantId = request.tenantContext?.tenantId;

      if (!tenantId) {
        throw new VehicleOperationNotAllowedException(
          request.user?.id || 'unknown',
          'getVehicleList',
          'Insufficient tenant access permissions',
        );
      }

      const vehicles =
        await this.vehiclesService.getAllVehiclesMinimal(tenantId);

      return { data: vehicles };
    } catch (error) {
      throw error;
    }
  }

  @Get('generateFleetId')
  @ApiOperation({ summary: 'Generate a unique fleet ID for a new vehicle' })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({ description: 'OK', type: GenerateFleetIdDto })
  async generateFleetId(): Promise<GenerateFleetIdDto> {
    const fleetId = await this.vehiclesService.generateFleetId();
    return { fleetId };
  }

  @Get(':vehicleId')
  @ApiOperation({ summary: 'Find vehicle by vehicle Id' })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({ description: 'OK', type: GetVehicleDto })
  async getVehicleDetails(
    @Param('vehicleId') vehicleId: string,
  ): Promise<GetVehicleDto> {
    try {
      const responseDomain =
        await this.vehiclesService.getVehicleDetails(vehicleId);
      const response = this.mapper.map(
        responseDomain,
        VehicleDomain,
        GetVehicleDto,
      );
      return response;
    } catch (error) {
      throw error;
    }
  }

  @Put(':vehicleId')
  @ApiOperation({ summary: 'Update vehicle by vehicle Id' })
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiNoContentResponse({ description: 'No Content' })
  async updateVehicleDetails(
    @Param('vehicleId') vehicleId: string,
    @Body() updateVehicleDto: CreateVehicleDto,
  ): Promise<void> {
    try {
      const vehicle = this.mapper.map(
        updateVehicleDto,
        CreateVehicleDto,
        VehicleDomain,
      );
      vehicle.id = vehicleId;
      await this.vehiclesService.updateVehicleDetails(vehicle);
      return;
    } catch (error) {
      throw error;
    }
  }

  @Delete(':vehicleId')
  @ApiOperation({ summary: 'Soft-delete vehicle by vehicle Id' })
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiNoContentResponse({ description: 'No Content' })
  async deleteVehicle(@Param('vehicleId') vehicleId: string): Promise<void> {
    try {
      await this.vehiclesService.deleteVehicle(vehicleId);
      return;
    } catch (error) {
      throw error;
    }
  }
}
