import { Injectable } from '@nestjs/common';
import { OrderRepository } from './infrastructure/repositories/order.repository';
import { OrderItemRepository } from './infrastructure/repositories/order-item.repository';
import { OrderStatusHistoryRepository } from './infrastructure/repositories/order-status-history.repository';
import { OrderAssignmentHistoryRepository } from './infrastructure/repositories/order-assignment-history.repository';
import { OrderStopHistoryRepository } from './infrastructure/repositories/order-stop-history.repository';
import { TrackingNumberService } from './services/tracking-number.service';
import { OrderStatusService } from './services/order-status.service';
import { OrderAssignmentService } from './services/order-assignment.service';
import { AddressService } from '@app/business/address/addresses/address.service';
import { calculateAddressDistanceById } from '@utils/distance.utils';
import { PriceModifiersService } from '@app/business/pricing/price-modifiers/price-modifiers.service';
import { OrderDetailsDto } from '@app/business/pricing/price-modifiers/dto/calculate-price.dto';
import { CreateOrderDto } from './dto/create-order.dto';
import { EnhancedOrderDto } from './dto/enhanced-order.dto';
import { UpdateOrderDto } from './dto/update-order.dto';
import { OrderResponseDto } from './dto/order-response.dto';
import { OrderDetailResponseDto } from './dto/order-detail-response.dto';
import { CreateOrderItemDto } from './dto/create-order-item.dto';
import { UpdateOrderItemDto } from './dto/update-order-item.dto';
import {
  OrderStatus,
  OrderStopType,
  BillingStatus,
  PaymentStatus,
  DistanceUnit,
} from './domain/order.types';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { DataSource } from 'typeorm';
import { BaseFilterDto } from '@core/infrastructure/filtering/dtos/base-filter.dto';
import { PaginatedResult } from '@utils/query-creator/interfaces';
import { Order } from './domain/order';
import { OrderItem } from './domain/order-item';
import {
  OrderNotFoundException,
  OrderTenantMismatchException,
  OrderLockedException,
  InvalidOrderStatusTransitionException,
  OrderUpdateFailedException,
  OrderCreationFailedException,
  InvalidOrderDataException,
  OrderItemNotFoundException,
  OrderDeliveryTimeInvalidException,
} from '@utils/errors/exceptions/order.exceptions';
import { DraftOrderResponseDto } from '@app/business/order/orders/dto/draft-order-response.dto';
import { DraftOrderDto } from '@app/business/order/orders/dto/draft-order.dto';

@Injectable()
export class OrdersService {
  constructor(
    private readonly orderRepository: OrderRepository,
    private readonly orderItemRepository: OrderItemRepository,
    private readonly orderStatusHistoryRepository: OrderStatusHistoryRepository,
    private readonly orderAssignmentHistoryRepository: OrderAssignmentHistoryRepository,
    private readonly orderStopHistoryRepository: OrderStopHistoryRepository,
    private readonly trackingNumberService: TrackingNumberService,
    private readonly orderStatusService: OrderStatusService,
    private readonly orderAssignmentService: OrderAssignmentService,
    private readonly addressService: AddressService,
    private readonly connection: DataSource,
    private readonly eventEmitter: EventEmitter2,
    private readonly priceModifiersService: PriceModifiersService,
  ) {}

  async createOrder(
    tenantId: string,
    userId: string,
    createOrderDto: EnhancedOrderDto,
  ): Promise<OrderResponseDto> {
    console.log('DEBUG - Order foreign key references:');
    console.log(`Tenant ID: ${tenantId}`);
    console.log(`Customer ID: ${createOrderDto.customerId}`);
    console.log(`Requested By ID: ${createOrderDto.requestedById}`);
    console.log(`Submitted By ID: ${createOrderDto.submittedById}`);
    console.log(
      `Package Template ID: No longer used - supporting multiple packages`,
    );

    const collectionAddressId = createOrderDto.collectionAddressId;
    console.log(`Collection Address ID: ${collectionAddressId}`);

    const deliveryAddressId = createOrderDto.deliveryAddressId;
    console.log(`Delivery Address ID: ${deliveryAddressId}`);

    let totalItems = 1;
    let totalWeight = 0;
    let totalVolume = 0;

    if (createOrderDto.items && createOrderDto.items.length > 0) {
      totalItems = createOrderDto.items.reduce(
        (sum, item) => sum + (item.quantity || 1),
        0,
      );

      totalWeight = createOrderDto.items.reduce(
        (sum, item) => sum + (item.weight || 0) * (item.quantity || 1),
        0,
      );

      totalVolume = createOrderDto.items.reduce((sum, item) => {
        if (item.length && item.width && item.height) {
          const itemVolume =
            item.length * item.width * item.height * (item.quantity || 1);
          return sum + itemVolume;
        }
        return sum;
      }, 0);
    }

    console.log(`Calculated Total Items: ${totalItems}`);
    console.log(`Calculated Total Weight: ${totalWeight}`);
    console.log(`Calculated Total Volume: ${totalVolume}`);

    console.log(`Vehicle Type ID: No longer provided by frontend`);
    console.log(`Assigned Driver ID: No longer provided by frontend`);
    console.log(`Assigned Vehicle ID: No longer provided by frontend`);
    console.log(`Price Set ID: ${createOrderDto.priceSetId || 'NULL'}`);

    const queryRunner = this.connection.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      if (createOrderDto.customerId) {
        const customerExists = await queryRunner.manager.query(
          'SELECT EXISTS(SELECT 1 FROM users WHERE id = $1)',
          [createOrderDto.customerId],
        );
        if (!customerExists[0].exists) {
          throw new OrderCreationFailedException(
            `Customer with ID ${createOrderDto.customerId} does not exist`,
          );
        }
      }

      if (createOrderDto.requestedById) {
        const contactExists = await queryRunner.manager.query(
          'SELECT EXISTS(SELECT 1 FROM contacts WHERE id = $1)',
          [createOrderDto.requestedById],
        );
        if (!contactExists[0].exists) {
          throw new OrderCreationFailedException(
            `Contact (requested_by) with ID ${createOrderDto.requestedById} does not exist`,
          );
        }
      }

      if (createOrderDto.submittedById) {
        const contactExists = await queryRunner.manager.query(
          'SELECT EXISTS(SELECT 1 FROM contacts WHERE id = $1)',
          [createOrderDto.submittedById],
        );
        if (!contactExists[0].exists) {
          throw new OrderCreationFailedException(
            `Contact (submitted_by) with ID ${createOrderDto.submittedById} does not exist`,
          );
        }
      }

      if (createOrderDto.collectionAddressId) {
        const addressExists = await queryRunner.manager.query(
          'SELECT EXISTS(SELECT 1 FROM addresses WHERE id = $1)',
          [createOrderDto.collectionAddressId],
        );
        if (!addressExists[0].exists) {
          throw new OrderCreationFailedException(
            `Collection address with ID ${createOrderDto.collectionAddressId} does not exist`,
          );
        }
      }

      if (createOrderDto.collectionZoneId) {
        const zoneExists = await queryRunner.manager.query(
          'SELECT EXISTS(SELECT 1 FROM zones WHERE id = $1)',
          [createOrderDto.collectionZoneId],
        );
        if (!zoneExists[0].exists) {
          throw new OrderCreationFailedException(
            `Collection zone with ID ${createOrderDto.collectionZoneId} does not exist`,
          );
        }
      }

      if (createOrderDto.deliveryAddressId) {
        const addressExists = await queryRunner.manager.query(
          'SELECT EXISTS(SELECT 1 FROM addresses WHERE id = $1)',
          [createOrderDto.deliveryAddressId],
        );
        if (!addressExists[0].exists) {
          throw new OrderCreationFailedException(
            `Delivery address with ID ${createOrderDto.deliveryAddressId} does not exist`,
          );
        }
      }

      if (createOrderDto.deliveryZoneId) {
        const zoneExists = await queryRunner.manager.query(
          'SELECT EXISTS(SELECT 1 FROM zones WHERE id = $1)',
          [createOrderDto.deliveryZoneId],
        );
        if (!zoneExists[0].exists) {
          throw new OrderCreationFailedException(
            `Delivery zone with ID ${createOrderDto.deliveryZoneId} does not exist`,
          );
        }
      }

      if (createOrderDto.priceSetId) {
        const priceSetExists = await queryRunner.manager.query(
          'SELECT EXISTS(SELECT 1 FROM price_sets WHERE id = $1)',
          [createOrderDto.priceSetId],
        );
        if (!priceSetExists[0].exists) {
          throw new OrderCreationFailedException(
            `Price set with ID ${createOrderDto.priceSetId} does not exist`,
          );
        }
      }

      const trackingNumber =
       await this.trackingNumberService.generateTrackingNumber();

      const order = new Order();
      order.tenantId = tenantId;
      order.trackingNumber = trackingNumber;
      order.referenceNumber = createOrderDto.referenceNumber;
      order.customerId = createOrderDto.customerId;
      order.requestedById = createOrderDto.requestedById;
      order.submittedById = createOrderDto.submittedById;

      if (!collectionAddressId) {
        throw new OrderCreationFailedException(
          'Collection address ID is required.',
        );
      }
      order.collectionAddressId = collectionAddressId;
      order.collectionContactName = createOrderDto.collectionContactName;
      order.collectionInstructions = createOrderDto.collectionInstructions;
      order.collectionSignatureRequired =
        createOrderDto.collectionSignatureRequired ?? false;
      order.scheduledCollectionTime = createOrderDto.scheduledCollectionTime;

      order.collectionZoneId = undefined;

      if (!deliveryAddressId) {
        throw new OrderCreationFailedException(
          'Delivery address ID is required.',
        );
      }
      order.deliveryAddressId = deliveryAddressId;
      order.deliveryContactName = createOrderDto.deliveryContactName;
      order.deliveryInstructions = createOrderDto.deliveryInstructions;
      order.deliverySignatureRequired =
        createOrderDto.deliverySignatureRequired ?? false;
      order.scheduledDeliveryTime = (
        createOrderDto as any
      ).scheduledDeliveryTime;

      order.deliveryZoneId = undefined;

      order.totalItems = totalItems;
      order.totalWeight = totalWeight;
      order.totalVolume = totalVolume;
      order.declaredValue = createOrderDto.declaredValue;
      order.codAmount = createOrderDto.codAmount;
      order.codCollected = false;
      order.priceSetId = createOrderDto.priceSetId;
      order.optionsPrice = 0;
      order.miscAdjustment = 0;
      order.customerAdjustment = 0;
      order.billingStatus = BillingStatus.NotBilled;
      order.paymentStatus = PaymentStatus.Pending;

      if (collectionAddressId && deliveryAddressId) {
        try {
          console.log(
            'Calculating distance between addresses using Google Maps API...',
          );

          const collectionAddress =
            await this.addressService.getAddressDetails(collectionAddressId);

          const deliveryAddress =
            await this.addressService.getAddressDetails(deliveryAddressId);

          const calculatedDistance = await calculateAddressDistanceById(
            this.addressService,
            collectionAddressId,
            deliveryAddressId,
            DistanceUnit.Kilometers,
            collectionAddress?.latitude,
            collectionAddress?.longitude,
            deliveryAddress?.latitude,
            deliveryAddress?.longitude,
          );

          if (calculatedDistance > 0) {
            console.log(
              `Calculated distance: ${calculatedDistance} ${DistanceUnit.Kilometers}`,
            );
            order.distance = calculatedDistance;
          } else {
            console.log(
              'Could not calculate distance between addresses (no route found or missing coordinates)',
            );
          }
        } catch (error) {
          console.error('Error calculating distance between addresses:', error);
        }
      } else {
      }

      order.description = createOrderDto.description;
      order.comments = createOrderDto.comments;
      order.internalNotes = createOrderDto.internalNotes;
      order.customFields = createOrderDto.customFields || {};
      order.metadata = createOrderDto.metadata || {};
      order.isLocked = false;
      order.isDeleted = false;
      order.createdBy = userId;

      console.log(
        'DEBUG - About to create order with the following data:',
        JSON.stringify(order, null, 2),
      );
      const createdOrder = await this.orderRepository.create(order);

      await this.orderStatusHistoryRepository.create(
        createdOrder.id,
        null,
        createdOrder.status,
        userId,
        'Order created',
      );

      if (createOrderDto.items && createOrderDto.items.length > 0) {
        await this.orderItemRepository.createBulk(
          createdOrder.id,
          createOrderDto.items,
        );

        const totals = await this.orderItemRepository.calculateOrderTotals(
          createdOrder.id,
        );

        if (totals) {
          createdOrder.totalItems = totals.totalItems;
          createdOrder.totalWeight = totals.totalWeight;
          createdOrder.totalVolume = totals.totalVolume;
          createdOrder.declaredValue = totals.totalDeclaredValue;

          await this.orderRepository.update(createdOrder.id, tenantId, {
            totalItems: totals.totalItems,
            totalWeight: totals.totalWeight,
            totalVolume: totals.totalVolume,
            declaredValue: totals.totalDeclaredValue,
          });
        }
      }

      if (createOrderDto.scheduledCollectionTime) {
        await this.orderStopHistoryRepository.create(
          createdOrder.id,
          OrderStopType.Collection,
          createOrderDto.scheduledCollectionTime,
          userId,
        );
      }

      if ((createOrderDto as any).scheduledDeliveryTime) {
        await this.orderStopHistoryRepository.create(
          createdOrder.id,
          OrderStopType.Delivery,
          (createOrderDto as any).scheduledDeliveryTime,
          userId,
        );
      }

      await queryRunner.commitTransaction();

      try {
        const orderDetailsForPricing: OrderDetailsDto = {
          id: createdOrder.id,
          basePrice: createdOrder.basePrice || 0,
          declaredPrice: createdOrder.declaredValue || 0,
          weight: createdOrder.totalWeight || 0,
          distance: createdOrder.distance || 0,
          quantity: createdOrder.totalItems || 1,
        };

        let priceModifierIds: string[] = [];
        if (createdOrder.priceSetId) {
          priceModifierIds = [];
        }

        const calculationResult =
          await this.priceModifiersService.calculatePrice(
            tenantId,
            orderDetailsForPricing,
            priceModifierIds,
          );

        if (calculationResult) {
          createdOrder.basePrice = calculationResult.basePrice;
          createdOrder.totalPrice = calculationResult.totalPrice;

          let optionsPrice = 0;
          calculationResult.modifiers.forEach((modifier) => {
            optionsPrice += modifier.amount;
          });
          createdOrder.optionsPrice = optionsPrice;

          await this.orderRepository.update(createdOrder.id, tenantId, {
            basePrice: calculationResult.basePrice,
            optionsPrice: optionsPrice,
            totalPrice: calculationResult.totalPrice,
          });

          console.log(
            `Price calculation completed. Total price: ${calculationResult.totalPrice}`,
          );
        }
      } catch (priceError) {
        console.error('Error calculating price for order:', priceError);
      }

      const orderItems = await this.orderItemRepository.findByOrderId(
        createdOrder.id,
      );
      const orderDetails = await this.findOne(tenantId, createdOrder.id);

      this.eventEmitter.emit('order.created', {
        order: {
          ...orderDetails,
          items: orderItems,
          collection: {
            contactName: createdOrder.collectionContactName,
            date: createdOrder.scheduledCollectionTime,
          },
          delivery: {
            contactName: createdOrder.deliveryContactName,
            date: createdOrder.scheduledDeliveryTime,
          },
        },
        tenantId: createdOrder.tenantId,
      });

      const orderResponse = this.mapOrderToResponseDto(createdOrder);

      return orderResponse;
    } catch (error) {
      await queryRunner.rollbackTransaction();

      console.error('DEBUG - Order creation failed with error:', error);

      if (error instanceof OrderDeliveryTimeInvalidException) {
        throw error;
      }

      if (error.message && error.message.includes('foreign key constraint')) {
        console.error(
          'DEBUG - Foreign key constraint violation details:',
          error,
        );

        const constraintMatch = error.message.match(/constraint "([^"]+)"/);
        const constraintName = constraintMatch ? constraintMatch[1] : 'unknown';

        throw new OrderCreationFailedException(
          `Foreign key constraint violation (${constraintName}). One or more referenced entities do not exist.`,
        );
      }

      if (
        error.message &&
        error.message.includes('violates foreign key constraint')
      ) {
        console.error('DEBUG - Foreign key issue details:', error.message);

        if (error.message.includes('assigned_driver_id')) {
          throw new OrderCreationFailedException(
            'Error with assigned driver ID. This field is optional and can be left empty during order creation.',
          );
        } else {
          throw new OrderCreationFailedException(error.message);
        }
      } else {
        throw new OrderCreationFailedException(error.message);
      }
    } finally {
      await queryRunner.release();
    }
  }

  async findAll(
    tenantId: string,
    filter: BaseFilterDto,
  ): Promise<PaginatedResult<OrderResponseDto>> {
    return this.orderRepository.findByTenantId(tenantId, filter);
  }

  async findOne(tenantId: string, id: string): Promise<OrderDetailResponseDto> {
    const order = await this.orderRepository.findById(id);

    if (!order) {
      throw new OrderNotFoundException(id);
    }

    if (order.tenantId !== tenantId) {
      throw new OrderTenantMismatchException(id, tenantId);
    }

    const items = await this.orderItemRepository.findByOrderId(id);

    const statusHistory =
      await this.orderStatusHistoryRepository.findByOrderId(id);

    const assignmentHistory =
      await this.orderAssignmentHistoryRepository.findByOrderId(id);

    const stopHistory = await this.orderStopHistoryRepository.findByOrderId(id);

    const orderResponse = this.mapOrderToDetailResponseDto(order);

    if (items && items.length > 0) {
      orderResponse.items = items.map((item) => ({
        id: item.id,
        orderId: item.orderId,
        packageTemplateId: item.packageTemplateId,
        packageTemplateName: item.packageTemplateName,
        itemType: item.itemType,
        quantity: item.quantity,
        weight: item.weight,
        weightUnit: item.weightUnit,
        length: item.length,
        width: item.width,
        height: item.height,
        dimensionUnit: item.dimensionUnit,
        volume: item.volume,
        declaredValue: item.declaredValue,
        description: item.description,
        notes: item.notes,
        imageUrl: item.imageUrl,
        createdAt: item.createdAt,
        updatedAt: item.updatedAt,
      }));
    }

    if (statusHistory && statusHistory.length > 0) {
      orderResponse.statusHistory = statusHistory.map((history) => ({
        id: history.id,
        orderId: history.orderId,
        previousStatus: history.previousStatus,
        newStatus: history.newStatus,
        reason: history.reason,
        comments: history.comments,
        changedBy: history.changedBy,
        changedAt: history.changedAt,
      }));
    }

    if (assignmentHistory && assignmentHistory.length > 0) {
      orderResponse.assignmentHistory = assignmentHistory.map((history) => ({
        id: history.id,
        orderId: history.orderId,
        previousDriverId: history.previousAssigneeId || '',
        newDriverId: history.newAssigneeId || '',
        previousVehicleId: history.previousVehicleId || '',
        newVehicleId: history.assignedVehicleId || '',
        reason: history.reason,
        assignedBy: history.assignedById,
        assignedAt: history.createdAt,
      }));
    }

    if (stopHistory && stopHistory.length > 0) {
      orderResponse.stopHistory = stopHistory.map((history) => ({
        id: history.id,
        orderId: history.orderId,
        stopType: history.stopType,
        scheduledTime: history.scheduledTime,
        actualTime: history.actualTime,
        locationData: history.locationData,
        notes: history.notes,
        updatedBy: history.createdBy,
        updatedAt: history.createdAt,
      }));
    }

    return orderResponse;
  }

  async findByTrackingNumber(
    tenantId: string,
    trackingNumber: string,
  ): Promise<OrderDetailResponseDto> {
    const order =
      await this.orderRepository.findByTrackingNumber(trackingNumber);

    if (!order) {
      throw new OrderNotFoundException(
        `Order with tracking number: ${trackingNumber} not found`,
      );
    }

    if (order.tenantId !== tenantId) {
      throw new OrderTenantMismatchException(order.id, tenantId);
    }

    return this.findOne(tenantId, order.id);
  }

  async update(
    tenantId: string,
    id: string,
    userId: string,
    updateOrderDto: UpdateOrderDto,
  ): Promise<OrderResponseDto> {
    const order = await this.orderRepository.findById(id);

    if (!order) {
      throw new OrderNotFoundException(id);
    }

    if (order.tenantId !== tenantId) {
      throw new OrderTenantMismatchException(id, tenantId);
    }

    if (order.isLocked) {
      throw new OrderLockedException(id, order.lockedBy || '');
    }

    if (updateOrderDto.status && updateOrderDto.status !== order.status) {
      if (
        !this.orderStatusService.isValidTransition(
          order.status,
          updateOrderDto.status,
        )
      ) {
        throw new InvalidOrderStatusTransitionException(
          id,
          order.status,
          updateOrderDto.status,
        );
      }
    }

    if (
      updateOrderDto.scheduledCollectionTime &&
      updateOrderDto.scheduledDeliveryTime &&
      updateOrderDto.scheduledDeliveryTime <=
        updateOrderDto.scheduledCollectionTime
    ) {
      throw new OrderDeliveryTimeInvalidException();
    }

    if (
      updateOrderDto.scheduledDeliveryTime &&
      !updateOrderDto.scheduledCollectionTime &&
      order.scheduledCollectionTime &&
      updateOrderDto.scheduledDeliveryTime <= order.scheduledCollectionTime
    ) {
      throw new OrderDeliveryTimeInvalidException();
    }

    if (
      updateOrderDto.scheduledCollectionTime &&
      !updateOrderDto.scheduledDeliveryTime &&
      order.scheduledDeliveryTime &&
      order.scheduledDeliveryTime <= updateOrderDto.scheduledCollectionTime
    ) {
      throw new OrderDeliveryTimeInvalidException();
    }

    const queryRunner = this.connection.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const updateData: Partial<Order> = {
        updatedBy: userId,
      };

      if (updateOrderDto.referenceNumber !== undefined)
        updateData.referenceNumber = updateOrderDto.referenceNumber;
      if (updateOrderDto.packageTemplateId !== undefined)
        updateData.packageTemplateId = updateOrderDto.packageTemplateId;
      if (updateOrderDto.collectionAddressId !== undefined)
        updateData.collectionAddressId = updateOrderDto.collectionAddressId;
      if (updateOrderDto.collectionContactName !== undefined)
        updateData.collectionContactName = updateOrderDto.collectionContactName;
      if (updateOrderDto.collectionInstructions !== undefined)
        updateData.collectionInstructions =
          updateOrderDto.collectionInstructions;
      if (updateOrderDto.collectionSignatureRequired !== undefined)
        updateData.collectionSignatureRequired =
          updateOrderDto.collectionSignatureRequired;
      if (updateOrderDto.scheduledCollectionTime !== undefined)
        updateData.scheduledCollectionTime =
          updateOrderDto.scheduledCollectionTime;
      if (updateOrderDto.actualCollectionTime !== undefined)
        updateData.actualCollectionTime = updateOrderDto.actualCollectionTime;
      if (updateOrderDto.collectionZoneId !== undefined)
        updateData.collectionZoneId = updateOrderDto.collectionZoneId;
      if (updateOrderDto.deliveryAddressId !== undefined)
        updateData.deliveryAddressId = updateOrderDto.deliveryAddressId;
      if (updateOrderDto.deliveryContactName !== undefined)
        updateData.deliveryContactName = updateOrderDto.deliveryContactName;
      if (updateOrderDto.deliveryInstructions !== undefined)
        updateData.deliveryInstructions = updateOrderDto.deliveryInstructions;
      if (updateOrderDto.deliverySignatureRequired !== undefined)
        updateData.deliverySignatureRequired =
          updateOrderDto.deliverySignatureRequired;
      if (updateOrderDto.scheduledDeliveryTime !== undefined)
        updateData.scheduledDeliveryTime = updateOrderDto.scheduledDeliveryTime;
      if (updateOrderDto.actualDeliveryTime !== undefined)
        updateData.actualDeliveryTime = updateOrderDto.actualDeliveryTime;
      if (updateOrderDto.deliveryZoneId !== undefined)
        updateData.deliveryZoneId = updateOrderDto.deliveryZoneId;
      if (updateOrderDto.vehicleTypeId !== undefined)
        updateData.vehicleTypeId = updateOrderDto.vehicleTypeId;
      if (updateOrderDto.assignedDriverId !== undefined)
        updateData.assignedDriverId = updateOrderDto.assignedDriverId;
      if (updateOrderDto.assignedVehicleId !== undefined)
        updateData.assignedVehicleId = updateOrderDto.assignedVehicleId;
      if (updateOrderDto.codAmount !== undefined)
        updateData.codAmount = updateOrderDto.codAmount;
      if (updateOrderDto.codCollected !== undefined)
        updateData.codCollected = updateOrderDto.codCollected;
      if (updateOrderDto.codCollectionDate !== undefined)
        updateData.codCollectionDate = updateOrderDto.codCollectionDate;
      if (updateOrderDto.priceSetId !== undefined)
        updateData.priceSetId = updateOrderDto.priceSetId;
      if (updateOrderDto.basePriceType !== undefined)
        updateData.basePriceType = updateOrderDto.basePriceType;
      if (updateOrderDto.basePrice !== undefined)
        updateData.basePrice = updateOrderDto.basePrice;
      if (updateOrderDto.optionsPrice !== undefined)
        updateData.optionsPrice = updateOrderDto.optionsPrice;
      if (updateOrderDto.miscAdjustment !== undefined)
        updateData.miscAdjustment = updateOrderDto.miscAdjustment;
      if (updateOrderDto.customerAdjustment !== undefined)
        updateData.customerAdjustment = updateOrderDto.customerAdjustment;
      if (updateOrderDto.description !== undefined)
        updateData.description = updateOrderDto.description;
      if (updateOrderDto.comments !== undefined)
        updateData.comments = updateOrderDto.comments;
      if (updateOrderDto.internalNotes !== undefined)
        updateData.internalNotes = updateOrderDto.internalNotes;
      if (updateOrderDto.customFields !== undefined)
        updateData.customFields = updateOrderDto.customFields;
      if (updateOrderDto.metadata !== undefined)
        updateData.metadata = updateOrderDto.metadata;

      const updatedOrder = await this.orderRepository.update(
        id,
        tenantId,
        updateData,
      );

      if (!updatedOrder) {
        throw new OrderUpdateFailedException(
          id,
          'Failed to update order details',
        );
      }

      if (updateOrderDto.status && updateOrderDto.status !== order.status) {
        const statusUpdated = await this.orderStatusService.updateOrderStatus(
          updatedOrder,
          updateOrderDto.status,
          userId,
          'Status updated via order update',
        );

        if (!statusUpdated) {
          throw new OrderUpdateFailedException(
            id,
            'Failed to update order status',
          );
        }
      }

      if (updateOrderDto.items && updateOrderDto.items.length > 0) {
        await this.orderItemRepository.bulkUpdate(updateOrderDto.items);

        const totals = await this.orderItemRepository.calculateOrderTotals(id);

        if (totals) {
          await this.orderRepository.update(id, tenantId, {
            totalItems: totals.totalItems,
            totalWeight: totals.totalWeight,
            totalVolume: totals.totalVolume,
            declaredValue: totals.totalDeclaredValue,
            updatedBy: userId,
          });
        }
      }

      if (updateOrderDto.scheduledCollectionTime) {
        await this.orderStopHistoryRepository.create(
          updatedOrder.id,
          OrderStopType.Collection,
          updateOrderDto.scheduledCollectionTime,
          userId,
        );
      }

      if (updateOrderDto.scheduledDeliveryTime) {
        await this.orderStopHistoryRepository.create(
          updatedOrder.id,
          OrderStopType.Delivery,
          updateOrderDto.scheduledDeliveryTime,
          userId,
        );
      }

      await queryRunner.commitTransaction();

      const orderItems = await this.orderItemRepository.findByOrderId(id);
      const orderDetails = await this.findOne(tenantId, id);

      this.eventEmitter.emit('order.updated', {
        order: {
          ...orderDetails,
          items: orderItems,
          collection: {
            contactName: updatedOrder.collectionContactName ?? 'Contact',
            date: updatedOrder.scheduledCollectionTime,
          },
          delivery: {
            contactName: updatedOrder.deliveryContactName ?? 'Contact',
            date: updatedOrder.scheduledDeliveryTime,
          },
        },
        tenantId: updatedOrder.tenantId,
      });

      const resultOrder = await this.orderRepository.findById(id);
      const items = await this.orderItemRepository.findByOrderId(id);

      if (!resultOrder) {
        throw new OrderNotFoundException(id);
      }
      const orderResponse = this.mapOrderToResponseDto(resultOrder);

      if (items && items.length > 0) {
        orderResponse.items = items.map((item) => ({
          id: item.id,
          orderId: item.orderId,
          packageTemplateId: item.packageTemplateId,
          itemType: item.itemType,
          quantity: item.quantity,
          weight: item.weight,
          weightUnit: item.weightUnit,
          length: item.length,
          width: item.width,
          height: item.height,
          dimensionUnit: item.dimensionUnit,
          volume: item.volume,
          declaredValue: item.declaredValue,
          description: item.description,
          notes: item.notes,
          imageUrl: item.imageUrl,
          createdAt: item.createdAt,
          updatedAt: item.updatedAt,
        }));
      }

      return orderResponse;
    } catch (error) {
      await queryRunner.rollbackTransaction();

      if (
        error instanceof OrderNotFoundException ||
        error instanceof OrderTenantMismatchException ||
        error instanceof OrderLockedException ||
        error instanceof InvalidOrderStatusTransitionException ||
        error instanceof OrderUpdateFailedException ||
        error instanceof OrderDeliveryTimeInvalidException
      ) {
        throw error;
      }

      throw new OrderUpdateFailedException(id, error.message);
    } finally {
      await queryRunner.release();
    }
  }

  async remove(tenantId: string, id: string, userId: string): Promise<void> {
    const order = await this.orderRepository.findById(id);

    if (!order) {
      throw new OrderNotFoundException(id);
    }

    if (order.tenantId !== tenantId) {
      throw new OrderTenantMismatchException(id, tenantId);
    }

    if (order.isLocked) {
      throw new OrderLockedException(id, order.lockedBy || '');
    }

    const deleted = await this.orderRepository.softDelete(id, tenantId, userId);

    if (!deleted) {
      throw new OrderUpdateFailedException(id, 'Failed to delete order');
    }

    this.eventEmitter.emit('order.deleted', {
      orderId: id,
      tenantId,
      userId,
    });
  }

  async restore(
    tenantId: string,
    id: string,
    userId: string,
  ): Promise<OrderResponseDto> {
    const restored = await this.orderRepository.restore(id, tenantId, userId);

    if (!restored) {
      throw new OrderNotFoundException(id);
    }

    const order = await this.orderRepository.findById(id);

    if (!order || order.tenantId !== tenantId) {
      throw new OrderNotFoundException(id);
    }

    const orderResponse = this.mapOrderToResponseDto(order);

    this.eventEmitter.emit('order.restored', {
      orderId: id,
      tenantId,
      userId,
    });

    return orderResponse;
  }

  async hardDelete(tenantId: string, id: string): Promise<void> {
    const order = await this.orderRepository.findById(id);

    if (!order) {
      throw new OrderNotFoundException(id);
    }

    if (order.tenantId !== tenantId) {
      throw new OrderTenantMismatchException(id, tenantId);
    }

    const queryRunner = this.connection.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      await this.orderItemRepository.deleteByOrderId(id);
      await this.orderStatusHistoryRepository.deleteByOrderId(id);
      await this.orderAssignmentHistoryRepository.deleteByOrderId(id);
      await this.orderStopHistoryRepository.deleteByOrderId(id);

      const deleted = await this.orderRepository.hardDelete(id, tenantId);

      if (!deleted) {
        throw new OrderUpdateFailedException(
          id,
          'Failed to permanently delete order',
        );
      }

      await queryRunner.commitTransaction();

      this.eventEmitter.emit('order.permanently.deleted', {
        orderId: id,
        tenantId,
      });
    } catch (error) {
      await queryRunner.rollbackTransaction();

      if (
        error instanceof OrderNotFoundException ||
        error instanceof OrderTenantMismatchException ||
        error instanceof OrderUpdateFailedException
      ) {
        throw error;
      }

      throw new OrderUpdateFailedException(
        id,
        `Failed to permanently delete order: ${error.message}`,
      );
    } finally {
      await queryRunner.release();
    }
  }

  async changeStatus(
    tenantId: string,
    id: string,
    userId: string,
    status: OrderStatus,
    reason?: string,
    comments?: string,
  ): Promise<OrderResponseDto> {
    const order = await this.orderRepository.findById(id);

    if (!order) {
      throw new OrderNotFoundException(id);
    }

    if (order.tenantId !== tenantId) {
      throw new OrderTenantMismatchException(id, tenantId);
    }

    if (order.isLocked) {
      throw new OrderLockedException(id, order.lockedBy || '');
    }

    const previousStatus = order.status;

    const updatedOrder = await this.orderStatusService.updateOrderStatus(
      order,
      status,
      userId,
      reason,
      comments,
    );

    if (!updatedOrder) {
      throw new InvalidOrderStatusTransitionException(
        order.id,
        order.status,
        status,
      );
    }

    const refreshedOrder = await this.orderRepository.findById(id);

    if (!refreshedOrder) {
      throw new OrderNotFoundException(order.id);
    }

    const orderItems = await this.orderItemRepository.findByOrderId(id);
    const orderDetails = await this.findOne(tenantId, id);

    this.eventEmitter.emit('order.status.changed', {
      order: {
        ...orderDetails,
        items: orderItems,
        previousStatus,
        currentStatus: status,
        statusNotes: comments || reason,
        collection: {
          contactName: refreshedOrder.collectionContactName ?? 'Contact',
          date: refreshedOrder.scheduledCollectionTime,
        },
        delivery: {
          contactName: refreshedOrder.deliveryContactName ?? 'Contact',
          date: refreshedOrder.scheduledDeliveryTime,
        },
      },
      tenantId,
      previousStatus,
      currentStatus: status,
      notes: comments || reason,
    });

    return this.mapOrderToResponseDto(refreshedOrder);
  }

  async addOrderItem(
    tenantId: string,
    orderId: string,
    userId: string,
    createItemDto: CreateOrderItemDto,
  ): Promise<OrderItem> {
    const order = await this.orderRepository.findById(orderId);

    if (!order) {
      throw new OrderNotFoundException(orderId);
    }

    if (order.tenantId !== tenantId) {
      throw new OrderTenantMismatchException(orderId, tenantId);
    }

    if (order.isLocked) {
      throw new OrderLockedException(orderId, order.lockedBy || '');
    }

    const newItem = await this.orderItemRepository.create(
      orderId,
      createItemDto,
    );

    const totals = await this.orderItemRepository.calculateOrderTotals(orderId);

    if (totals) {
      await this.orderRepository.update(orderId, tenantId, {
        totalItems: totals.totalItems,
        totalWeight: totals.totalWeight,
        totalVolume: totals.totalVolume,
        declaredValue: totals.totalDeclaredValue,
        updatedBy: userId,
      });
    }

    return newItem;
  }

  async updateOrderItem(
    tenantId: string,
    orderId: string,
    userId: string,
    updateItemDto: UpdateOrderItemDto,
  ): Promise<OrderItem> {
    const order = await this.orderRepository.findById(orderId);

    if (!order) {
      throw new OrderNotFoundException(orderId);
    }

    if (order.tenantId !== tenantId) {
      throw new OrderTenantMismatchException(orderId, tenantId);
    }

    if (order.isLocked) {
      throw new OrderLockedException(orderId, order.lockedBy || '');
    }

    const item = await this.orderItemRepository.findById(updateItemDto.id);

    if (!item) {
      throw new OrderItemNotFoundException(updateItemDto.id);
    }

    if (item.orderId !== orderId) {
      throw new InvalidOrderDataException(
        `Item with ID ${updateItemDto.id} does not belong to order ${orderId}`,
        'itemId',
      );
    }

    const updatedItem = await this.orderItemRepository.update(
      updateItemDto.id,
      updateItemDto,
    );

    if (!updatedItem) {
      throw new OrderUpdateFailedException(
        orderId,
        `Failed to update item ${updateItemDto.id}`,
      );
    }

    const totals = await this.orderItemRepository.calculateOrderTotals(orderId);
    console.log({
      totalItems: totals.totalItems,
      totalWeight: totals.totalWeight,
      totalVolume: totals.totalVolume,
      declaredValue: totals.totalDeclaredValue,
      updatedBy: userId,
    });

    if (totals) {
      await this.orderRepository.update(orderId, tenantId, {
        totalItems: totals.totalItems,
        totalWeight: totals.totalWeight,
        totalVolume: totals.totalVolume,
        declaredValue: totals.totalDeclaredValue,
        updatedBy: userId,
      });
    }

    return updatedItem;
  }

  async removeOrderItem(
    tenantId: string,
    orderId: string,
    userId: string,
    itemId: string,
  ): Promise<void> {
    const order = await this.orderRepository.findById(orderId);

    if (!order) {
      throw new OrderNotFoundException(orderId);
    }

    if (order.tenantId !== tenantId) {
      throw new OrderTenantMismatchException(orderId, tenantId);
    }

    if (order.isLocked) {
      throw new OrderLockedException(orderId, order.lockedBy || '');
    }

    const item = await this.orderItemRepository.findById(itemId);

    if (!item) {
      throw new OrderItemNotFoundException(itemId);
    }

    if (item.orderId !== orderId) {
      throw new InvalidOrderDataException(
        `Item with ID ${itemId} does not belong to order ${orderId}`,
        'itemId',
      );
    }

    const deleted = await this.orderItemRepository.delete(itemId);

    if (!deleted) {
      throw new OrderUpdateFailedException(
        orderId,
        `Failed to delete item ${itemId}`,
      );
    }

    const totals = await this.orderItemRepository.calculateOrderTotals(orderId);

    if (totals) {
      await this.orderRepository.update(orderId, tenantId, {
        totalItems: totals.totalItems,
        totalWeight: totals.totalWeight,
        totalVolume: totals.totalVolume,
        declaredValue: totals.totalDeclaredValue,
        updatedBy: userId,
      });
    }
  }

  async lockOrder(
    tenantId: string,
    id: string,
    userId: string,
    reason?: string,
  ): Promise<boolean> {
    const order = await this.orderRepository.findById(id);

    if (!order) {
      throw new OrderNotFoundException(id);
    }

    if (order.tenantId !== tenantId) {
      throw new OrderTenantMismatchException(id, tenantId);
    }

    if (order.isLocked) {
      throw new OrderLockedException(id, order.lockedBy || '');
    }

    return this.orderRepository.lockOrder(id, userId, reason);
  }

  async unlockOrder(
    tenantId: string,
    id: string,
    userId: string,
  ): Promise<boolean> {
    const order = await this.orderRepository.findById(id);

    if (!order) {
      throw new OrderNotFoundException(id);
    }

    if (order.tenantId !== tenantId) {
      throw new OrderTenantMismatchException(id, tenantId);
    }

    if (order.isLocked && order.lockedBy !== userId) {
      throw new OrderUpdateFailedException(
        id,
        `Order is locked by user ${order.lockedBy} and can only be unlocked by them or an admin`,
      );
    }

    return this.orderRepository.unlockOrder(id);
  }

  async getOrderStatusHistory(tenantId: string, id: string): Promise<any[]> {
    const order = await this.orderRepository.findById(id);

    if (!order) {
      throw new OrderNotFoundException(id);
    }

    if (order.tenantId !== tenantId) {
      throw new OrderTenantMismatchException(id, tenantId);
    }

    return this.orderStatusHistoryRepository.findByOrderId(id);
  }

  async getOrderAssignmentHistory(
    tenantId: string,
    id: string,
  ): Promise<any[]> {
    const order = await this.orderRepository.findById(id);

    if (!order) {
      throw new OrderNotFoundException(id);
    }

    if (order.tenantId !== tenantId) {
      throw new OrderTenantMismatchException(id, tenantId);
    }

    return this.orderAssignmentHistoryRepository.findByOrderId(id);
  }

  async getOrderStopHistory(tenantId: string, id: string): Promise<any[]> {
    const order = await this.orderRepository.findById(id);

    if (!order) {
      throw new OrderNotFoundException(id);
    }

    if (order.tenantId !== tenantId) {
      throw new OrderTenantMismatchException(id, tenantId);
    }

    return this.orderStopHistoryRepository.findByOrderId(id);
  }

  async createDraftOrder(
    tenantId: string,
    userId: string,
    draftOrderDto: DraftOrderDto,
  ): Promise<DraftOrderResponseDto> {
    const queryRunner = this.connection.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const trackingNumber =
       await this.trackingNumberService.generateTrackingNumber();

      const order = new Order();
      order.tenantId = tenantId;
      order.trackingNumber = trackingNumber;
      order.referenceNumber = draftOrderDto.referenceNumber;
      order.customerId = draftOrderDto.customerId || '';
      order.requestedById = userId;
      order.submittedById = userId;

      order.collectionAddressId = draftOrderDto.collectionAddressId;
      order.collectionContactName = draftOrderDto.collectionContactName;
      order.collectionInstructions = draftOrderDto.collectionInstructions;

      order.deliveryAddressId = draftOrderDto.deliveryAddressId;
      order.deliveryContactName = draftOrderDto.deliveryContactName;
      order.deliveryInstructions = draftOrderDto.deliveryInstructions;


      order.isCod = draftOrderDto.isCod;
      order.codAmount = draftOrderDto.codAmount;
      order.isInsurance = draftOrderDto.isInsurance;
      order.declaredValue = draftOrderDto.declaredValue;
      order.internalNotes = draftOrderDto.internalNotes;

      order.description = draftOrderDto.description;
      order.comments = draftOrderDto.comments;

      order.optionsPrice = 0;
      order.miscAdjustment = 0;
      order.customerAdjustment = 0;
      order.billingStatus = BillingStatus.NotBilled;
      order.paymentStatus = PaymentStatus.Pending;
      order.distanceUnit = DistanceUnit.Kilometers;
      
      order.customFields = {};
      order.metadata = {
        isDraft: true,
        draftCreatedAt: new Date().toISOString(),
        completionRequired: [
          'package_details',
          'pickup_time',
          'delivery_time',
          'pricing',
        ],
      };
      order.isLocked = false;
      order.isDeleted = false;
      order.createdBy = userId;

      const createdOrder = await this.orderRepository.create(order);

      if (draftOrderDto.items && draftOrderDto.items.length > 0) {
        await this.orderItemRepository.createBulk(
          createdOrder.id,
          draftOrderDto.items,          
        );
      }
      await this.orderStatusHistoryRepository.create(
        createdOrder.id,
        null,
        OrderStatus.Draft,
        userId,
        'Draft order created - awaiting completion',
        'Order created with basic pickup and delivery information',
      );

      await queryRunner.commitTransaction();

      this.eventEmitter.emit('order.draft.created', {
        orderId: createdOrder.id,
        tenantId: createdOrder.tenantId,
        customerId: createdOrder.customerId,
        trackingNumber: createdOrder.trackingNumber,
        userId,
      });

      return this.mapOrderToDraftResponseDto(createdOrder);
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw new OrderCreationFailedException(
        `Failed to create draft order: ${error.message}`,
      );
    } finally {
      await queryRunner.release();
    }
  }

  private mapOrderToDraftResponseDto(order: Order): DraftOrderResponseDto {
    const nextSteps: string[] = [];

    if (order.totalItems === 0) {
      nextSteps.push('Add package details and items');
    }
    if (!order.scheduledCollectionTime) {
      nextSteps.push('Set pickup/collection time');
    }
    if (!order.scheduledDeliveryTime) {
      nextSteps.push('Set delivery time');
    }
    if (order.basePrice === 0) {
      nextSteps.push('Configure pricing and options');
    }
    if (!order.packageTemplateId) {
      nextSteps.push('Select package template');
    }

    return {
      id: order.id,
      tenantId: order.tenantId,
      trackingNumber: order.trackingNumber,
      referenceNumber: order.referenceNumber,
      status: order.status,
      customerId: order.customerId,
      collectionAddressId: order.collectionAddressId,
      collectionContactName: order.collectionContactName,
      collectionInstructions: order.collectionInstructions,
      deliveryAddressId: order.deliveryAddressId,
      deliveryContactName: order.deliveryContactName,
      deliveryInstructions: order.deliveryInstructions,
      description: order.description,
      comments: order.comments,
      createdAt: order.createdAt,
      updatedAt: order.updatedAt,
      isDraft: true,
      nextSteps,
    };
  }

  async completeDraftOrder(
    tenantId: string,
    draftOrderId: string,
    userId: string,
    completeOrderDto: CreateOrderDto | EnhancedOrderDto,
  ): Promise<OrderResponseDto> {
    const draftOrder = await this.orderRepository.findById(draftOrderId);
    if (!draftOrder) {
      throw new OrderNotFoundException(draftOrderId);
    }

    if (draftOrder.tenantId !== tenantId) {
      throw new OrderTenantMismatchException(draftOrderId, tenantId);
    }

    if (draftOrder.status !== OrderStatus.Draft) {
      throw new InvalidOrderDataException(
        `Order ${draftOrderId} is not a draft order. Current status: ${draftOrder.status}`,
        'status',
      );
    }

    const collectionAddressId =
      completeOrderDto.collectionAddressId || draftOrder.collectionAddressId;

    const deliveryAddressId =
      completeOrderDto.deliveryAddressId || draftOrder.deliveryAddressId;

    let totalItems = 1;
    let totalWeight = 0;
    let totalVolume = 0;

    if (completeOrderDto.items && completeOrderDto.items.length > 0) {
      totalItems = completeOrderDto.items.reduce(
        (sum, item) => sum + (item.quantity || 1),
        0,
      );

      totalWeight = completeOrderDto.items.reduce(
        (sum, item) => sum + (item.weight || 0) * (item.quantity || 1),
        0,
      );

      totalVolume = completeOrderDto.items.reduce((sum, item) => {
        if (item.length && item.width && item.height) {
          const itemVolume =
            item.length * item.width * item.height * (item.quantity || 1);
          return sum + itemVolume;
        }
        return sum;
      }, 0);
    }

    let calculatedDistance = 0;
    if (collectionAddressId && deliveryAddressId) {
      try {
        console.log(
          'Calculating distance between addresses for draft completion using Google Maps API...',
        );
        calculatedDistance = await calculateAddressDistanceById(
          this.addressService,
          collectionAddressId,
          deliveryAddressId,
          DistanceUnit.Kilometers,
        );

        if (calculatedDistance > 0) {
          console.log(
            `Calculated distance for draft completion: ${calculatedDistance} ${DistanceUnit.Kilometers}`,
          );
        } else {
          console.log(
            'Could not calculate distance between addresses for draft completion (no route found or missing coordinates)',
          );
        }
      } catch (error) {
        console.error(
          'Error calculating distance between addresses for draft completion:',
          error,
        );
      }
    }

    const queryRunner = this.connection.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const updateData: Partial<Order> = {
        referenceNumber:
          completeOrderDto.referenceNumber || draftOrder.referenceNumber,

        collectionAddressId,
        collectionContactName:
          completeOrderDto.collectionContactName ||
          draftOrder.collectionContactName,
        collectionInstructions:
          completeOrderDto.collectionInstructions ||
          draftOrder.collectionInstructions,
        collectionSignatureRequired:
          completeOrderDto.collectionSignatureRequired ?? false,
        scheduledCollectionTime: completeOrderDto.scheduledCollectionTime,

        deliveryAddressId,
        deliveryContactName:
          completeOrderDto.deliveryContactName ||
          draftOrder.deliveryContactName,
        deliveryInstructions:
          completeOrderDto.deliveryInstructions ||
          draftOrder.deliveryInstructions,
        deliverySignatureRequired:
          completeOrderDto.deliverySignatureRequired ?? false,
        scheduledDeliveryTime: (completeOrderDto as any).scheduledDeliveryTime,

        totalItems,
        totalWeight,
        totalVolume,
        declaredValue: completeOrderDto.declaredValue,

        codAmount: completeOrderDto.codAmount,

        priceSetId: completeOrderDto.priceSetId,

        distance: calculatedDistance,

        description: completeOrderDto.description || draftOrder.description,
        comments: completeOrderDto.comments || draftOrder.comments,
        internalNotes: completeOrderDto.internalNotes,

        customFields: completeOrderDto.customFields || {},
        metadata: {
          ...draftOrder.metadata,
          ...completeOrderDto.metadata,
          completedFromDraft: true,
          completedAt: new Date().toISOString(),
          completedBy: userId,
        },

        updatedBy: userId,
      };

      const updatedOrder = await this.orderRepository.update(
        draftOrderId,
        tenantId,
        updateData,
      );

      if (!updatedOrder) {
        throw new OrderUpdateFailedException(
          draftOrderId,
          'Failed to complete draft order',
        );
      }

      await this.orderStatusHistoryRepository.create(
        draftOrderId,
        OrderStatus.Draft,
        updatedOrder.status,
        userId,
        'Draft order completed',
        'Order moved from draft to active status with complete information',
      );

      if (completeOrderDto.items && completeOrderDto.items.length > 0) {
        await this.orderItemRepository.createBulk(
          draftOrderId,
          completeOrderDto.items,
        );

        const totals =
          await this.orderItemRepository.calculateOrderTotals(draftOrderId);
        if (totals) {
          await this.orderRepository.update(draftOrderId, tenantId, {
            totalItems: totals.totalItems,
            totalWeight: totals.totalWeight,
            totalVolume: totals.totalVolume,
            declaredValue: totals.totalDeclaredValue,
          });
        }
      }

      if (completeOrderDto.scheduledCollectionTime) {
        await this.orderStopHistoryRepository.create(
          draftOrderId,
          OrderStopType.Collection,
          completeOrderDto.scheduledCollectionTime,
          userId,
        );
      }

      if ((completeOrderDto as any).scheduledDeliveryTime) {
        await this.orderStopHistoryRepository.create(
          draftOrderId,
          OrderStopType.Delivery,
          (completeOrderDto as any).scheduledDeliveryTime,
          userId,
        );
      }

      await queryRunner.commitTransaction();

      this.eventEmitter.emit('order.draft.completed', {
        orderId: draftOrderId,
        tenantId: updatedOrder.tenantId,
        previousStatus: OrderStatus.Draft,
        currentStatus: updatedOrder.status,
        completedBy: userId,
      });

      const finalOrder = await this.orderRepository.findById(draftOrderId);
      if (!finalOrder) {
        throw new OrderNotFoundException(draftOrderId);
      }

      return this.mapOrderToResponseDto(finalOrder);
    } catch (error) {
      await queryRunner.rollbackTransaction();
      if (
        error instanceof OrderNotFoundException ||
        error instanceof OrderTenantMismatchException ||
        error instanceof InvalidOrderDataException ||
        error instanceof OrderUpdateFailedException ||
        error instanceof OrderDeliveryTimeInvalidException
      ) {
        throw error;
      }
      throw new OrderUpdateFailedException(
        draftOrderId,
        `Failed to complete draft order: ${error.message}`,
      );
    } finally {
      await queryRunner.release();
    }
  }
  private mapOrderToResponseDto(order: Order): OrderResponseDto {
    const dto = new OrderResponseDto();
    dto.id = order.id;
    dto.tenantId = order.tenantId;
    dto.trackingNumber = order.trackingNumber;
    dto.referenceNumber = order.referenceNumber;
    dto.status = order.status;
    dto.customerId = order.customerId;
    dto.customerName = order.customerName;
    dto.customerEmail = order.customerEmail;
    dto.customerPhoneNumber = order.customerPhoneNumber;
    dto.companyName = order.companyName;
    dto.customerContactName = order.customerContactName;
    dto.requestedById = order.requestedById;
    dto.collectionAddressId = order.collectionAddressId;
    dto.collectionAddress = order.collectionAddress;
    dto.collectionZoneName = order.collectionZoneName;
    dto.collectionZoneId = order.collectionZoneId;
    dto.collectionContactName = order.collectionContactName;
    dto.collectionCompanyName = order.collectionCompanyName;
    dto.collectionInstructions = order.collectionInstructions;
    dto.collectionSignatureRequired = order.collectionSignatureRequired;
    dto.scheduledCollectionTime = order.scheduledCollectionTime;
    dto.actualCollectionTime = order.actualCollectionTime;
    dto.deliveryAddressId = order.deliveryAddressId;
    dto.deliveryZoneName = order.deliveryZoneName;
    dto.deliveryAddress = order.deliveryAddress;
    dto.deliveryCompanyName = order.deliveryCompanyName;
    dto.deliveryZoneId = order.deliveryZoneId;
    dto.deliveryContactName = order.deliveryContactName;
    dto.deliveryInstructions = order.deliveryInstructions;
    dto.deliverySignatureRequired = order.deliverySignatureRequired;
    dto.scheduledDeliveryTime = order.scheduledDeliveryTime;
    dto.actualDeliveryTime = order.actualDeliveryTime;
    dto.packageTemplateId = order.packageTemplateId;
    dto.totalItems = order.totalItems;
    dto.totalWeight = order.totalWeight;
    dto.totalVolume = order.totalVolume;
    dto.declaredValue = order.declaredValue;
    dto.assignedDriverId = order.assignedDriverId;
    dto.assignedDriverName = order.assignedDriverName;
    dto.assignedVehicleId = order.assignedVehicleId;
    dto.assignedVehicleName = order.assignedVehicleName;
    dto.basePrice = order.basePrice;
    dto.isLocked = order.isLocked;
    dto.codCollected = order.codCollected;
    dto.assignedVehicleDescription = order.assignedVehicleDescription;
    dto.optionsPrice = order.optionsPrice;
    dto.miscAdjustment = order.miscAdjustment;
    dto.customerAdjustment = order.customerAdjustment;
    dto.totalPrice = order.totalPrice;
    dto.billingStatus = order.billingStatus;
    dto.paymentStatus = order.paymentStatus;
    dto.distance = order.distance;
    dto.distanceUnit = order.distanceUnit;
    dto.estimatedDuration = order.estimatedDuration;
    dto.description = order.description;
    dto.priceSetId = order.priceSetId;
    dto.priceSet = order.priceSet;
    dto.serviceLevel = order.priceSetInternalName;
    dto.comments = order.comments;
    dto.internalNotes = order.internalNotes;
    dto.codAmount = order.codAmount;
    dto.codCollected = order.codCollected;
    dto.invoiceNumber = order.invoiceNumber;
    dto.createdAt = order.createdAt;
    dto.updatedAt = order.updatedAt;

    return dto;
  }

  private mapOrderToDetailResponseDto(order: Order): OrderDetailResponseDto {
    const basicOrderResponse = this.mapOrderToResponseDto(order);

    const detailResponseDto = new OrderDetailResponseDto();
    Object.assign(detailResponseDto, basicOrderResponse);

    detailResponseDto.statusHistory = [];
    detailResponseDto.assignmentHistory = [];
    detailResponseDto.stopHistory = [];

    return detailResponseDto;
  }
}
