import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { OrderItemEntity } from '../entities/order-item.entity';
import { OrderItem } from '../../domain/order-item';
import { NullableType } from '@utils/types/nullable.type';
import { CreateOrderItemDto } from '../../dto/create-order-item.dto';
import { UpdateOrderItemDto } from '../../dto/update-order-item.dto';

@Injectable()
export class OrderItemRepository {
  constructor(
    @InjectRepository(OrderItemEntity)
    private readonly orderItemRepository: Repository<OrderItemEntity>,
  ) { }

  async create(
    orderId: string,
    itemData: CreateOrderItemDto,
  ): Promise<OrderItem> {
    const newItem = this.orderItemRepository.create({
      orderId,
      packageTemplateId: itemData.packageTemplateId,
      itemType: itemData.itemType,
      quantity: itemData.quantity,
      weight: itemData.weight,
      weightUnit: itemData.weightUnit || 'kg',
      length: itemData.length,
      width: itemData.width,
      height: itemData.height,
      dimensionUnit: itemData.dimensionUnit || 'cm',
      declaredValue: itemData.declaredValue,
      description: itemData.description,
      notes: itemData.notes,
      imageUrl: itemData.imageUrl,
      metadata: itemData.metadata || {},
    });

    const savedItem = await this.orderItemRepository.save(newItem);

    return this.mapToOrderItem(savedItem);
  }

  async createBulk(
    orderId: string,
    items: CreateOrderItemDto[],
  ): Promise<OrderItem[]> {
    const itemEntities = items.map((item) =>
      this.orderItemRepository.create({
        orderId,
        packageTemplateId: item.packageTemplateId,
        itemType: item.itemType,
        quantity: item.quantity,
        weight: item.weight,
        weightUnit: item.weightUnit || 'kg',
        length: item.length,
        width: item.width,
        height: item.height,
        dimensionUnit: item.dimensionUnit || 'cm',
        declaredValue: item.declaredValue,
        description: item.description,
        notes: item.notes,
        imageUrl: item.imageUrl,
        metadata: item.metadata || {},
      }),
    );

    const savedItems = await this.orderItemRepository.save(itemEntities);

    return savedItems.map((item) => this.mapToOrderItem(item));
  }

  async findByOrderId(orderId: string): Promise<OrderItem[]> {
    const items = await this.orderItemRepository.find({
      where: { orderId },
      relations: ['packageTemplate'],
    });

    return items.map((item) => this.mapToOrderItem(item));
  }

  async findById(id: string): Promise<NullableType<OrderItem>> {
    const item = await this.orderItemRepository.findOne({
      where: { id },
      relations: ['packageTemplate'],
    });

    return item ? this.mapToOrderItem(item) : null;
  }

  async update(
    id: string,
    itemData: UpdateOrderItemDto,
  ): Promise<NullableType<OrderItem>> {
    const item = await this.orderItemRepository.findOne({
      where: { id },
    });

    if (!item) {
      return null;
    }

    // Update only provided fields
    if (itemData.packageTemplateId !== undefined)
      item.packageTemplateId = itemData.packageTemplateId;
    if (itemData.itemType !== undefined) item.itemType = itemData.itemType;
    if (itemData.quantity !== undefined) item.quantity = itemData.quantity;
    if (itemData.weight !== undefined) item.weight = itemData.weight;
    if (itemData.weightUnit !== undefined)
      item.weightUnit = itemData.weightUnit;
    if (itemData.length !== undefined) item.length = itemData.length;
    if (itemData.width !== undefined) item.width = itemData.width;
    if (itemData.height !== undefined) item.height = itemData.height;
    if (itemData.dimensionUnit !== undefined)
      item.dimensionUnit = itemData.dimensionUnit;
    if (itemData.declaredValue !== undefined)
      item.declaredValue = itemData.declaredValue;
    if (itemData.description !== undefined)
      item.description = itemData.description;
    if (itemData.notes !== undefined) item.notes = itemData.notes;
    if (itemData.imageUrl !== undefined) item.imageUrl = itemData.imageUrl;
    if (itemData.metadata !== undefined) item.metadata = itemData.metadata;

    const updatedItem = await this.orderItemRepository.save(item);

    return this.mapToOrderItem(updatedItem);
  }

  async bulkUpdate(items: UpdateOrderItemDto[]): Promise<OrderItem[]> {
    const updatedItems: OrderItem[] = [];

    for (const itemData of items) {
      const item = await this.orderItemRepository.findOne({
        where: { id: itemData.id },
      });

      if (!item) {
        continue;
      }

      // Update only provided fields
      if (itemData.packageTemplateId !== undefined)
        item.packageTemplateId = itemData.packageTemplateId;
      if (itemData.itemType !== undefined) item.itemType = itemData.itemType;
      if (itemData.quantity !== undefined) item.quantity = itemData.quantity;
      if (itemData.weight !== undefined) item.weight = itemData.weight;
      if (itemData.weightUnit !== undefined)
        item.weightUnit = itemData.weightUnit;
      if (itemData.length !== undefined) item.length = itemData.length;
      if (itemData.width !== undefined) item.width = itemData.width;
      if (itemData.height !== undefined) item.height = itemData.height;
      if (itemData.dimensionUnit !== undefined)
        item.dimensionUnit = itemData.dimensionUnit;
      if (itemData.declaredValue !== undefined)
        item.declaredValue = itemData.declaredValue;
      if (itemData.description !== undefined)
        item.description = itemData.description;
      if (itemData.notes !== undefined) item.notes = itemData.notes;
      if (itemData.imageUrl !== undefined) item.imageUrl = itemData.imageUrl;
      if (itemData.metadata !== undefined) item.metadata = itemData.metadata;

      const updatedItem = await this.orderItemRepository.save(item);
      updatedItems.push(this.mapToOrderItem(updatedItem));
    }

    return updatedItems;
  }

  async delete(id: string): Promise<boolean> {
    const result = await this.orderItemRepository.delete(id);
    if (!result) {
      return true;
    }
    const isDeleted = result.affected;
    if (isDeleted) {
      return !!isDeleted;
    } else {
      return false;
    }
  }

  async deleteByOrderId(orderId: string): Promise<boolean> {
    const result = await this.orderItemRepository.delete({ orderId });
    if (!result) {
      return true;
    }
    const isDeleted = result.affected;
    if (isDeleted) {
      return !!isDeleted;
    } else {
      return false;
    }
  }


  async calculateOrderTotals(orderId: string): Promise<{
    totalItems: number;
    totalWeight: number;
    totalVolume: number;
    totalDeclaredValue: number;
  }> {
    const items = await this.orderItemRepository.find({ where: { orderId } });

    const totalItems = items.reduce((sum, item) => sum + (item.quantity || 0), 0);

    const totalWeight = items.reduce((sum, item) => {
      const raw = (item.weight || 0) * (item.quantity || 1);
      return sum + this.capDecimal(raw);
    }, 0);

    const totalVolume = items.reduce((sum, item) => {
      const raw = (item.volume || 0) * (item.quantity || 1);
      return sum + this.capDecimal(raw);
    }, 0);

    const totalDeclaredValue = items.reduce((sum, item) => {
      const raw = (item.declaredValue || 0) * (item.quantity || 1);
      return sum + this.capDecimal(raw);
    }, 0);


    return {
      totalItems,
      totalWeight: this.capDecimal(totalWeight),
      totalVolume: this.capDecimal(totalVolume),
      totalDeclaredValue: this.capDecimal(totalDeclaredValue),
    };
  }

  capDecimal(
    value: number | null | undefined,
    max = 99999999.99,
    decimals = 2,
  ): number {
    if (!value) return 0;
    const rounded = +value.toFixed(decimals);
    return Math.min(rounded, max);
  }
  

  private mapToOrderItem(entity: OrderItemEntity): OrderItem {
    const orderItem = new OrderItem();
    orderItem.id = entity.id;
    orderItem.orderId = entity.orderId;
    orderItem.packageTemplateId = entity.packageTemplateId;
    orderItem.packageTemplateName = entity.packageTemplate?.name;
    orderItem.itemType = entity.itemType;
    orderItem.quantity = entity.quantity;
    orderItem.weight = entity.weight;
    orderItem.weightUnit = entity.weightUnit;
    orderItem.length = entity.length;
    orderItem.width = entity.width;
    orderItem.height = entity.height;
    orderItem.dimensionUnit = entity.dimensionUnit;
    orderItem.volume = entity.volume;
    orderItem.declaredValue = entity.declaredValue;
    orderItem.description = entity.description;
    orderItem.notes = entity.notes;
    orderItem.imageUrl = entity.imageUrl;
    orderItem.metadata = entity.metadata;
    orderItem.createdAt = entity.createdAt;
    orderItem.updatedAt = entity.updatedAt;
    return orderItem;
  }
}
