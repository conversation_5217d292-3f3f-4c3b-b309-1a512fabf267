import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Delete,
  Query,
  HttpCode,
  HttpStatus,
  UseGuards,
  Req,
  Put,
  BadRequestException,
} from '@nestjs/common';
import { Request } from 'express';
import {
  ApiBearerAuth,
  ApiTags,
  ApiOperation,
  ApiParam,
  ApiCreatedResponse,
  ApiBody,
  ApiNoContentResponse,
  ApiForbiddenResponse,
  ApiNotFoundResponse,
  ApiOkResponse,
  ApiUnauthorizedResponse,
  ApiCookieAuth,
} from '@nestjs/swagger';
import { OrdersService } from './orders.service';
import { OrderResponseDto } from './dto/order-response.dto';
import { OrderDetailResponseDto } from './dto/order-detail-response.dto';
import { OrderPaymentService } from './services/order-payment.service';
import { CreateOrderPaymentDto } from './dto/create-order-payment.dto';
import { CreateOrderDto } from './dto/create-order.dto';
import { FindAllOrdersResponseDto } from './dto/find-all-orders-response.dto';
import { JwtContactAuthGuard } from '@core/auth/guards/jwt-contact-auth.guard';
import { ContactPermissionGuard } from '@app/business/user/contacts/guards/contact-permission.guard';
import { RequireContactPermission } from '@app/business/user/contacts/decorators/require-contact-permission.decorator';
import { CurrentUser } from '@core/auth/decorators/current-user.decorator';
import { JwtPayload } from '@core/auth/domain/auth.types';
import { ContactsService } from '@app/business/user/contacts/contacts.service';
import { BaseFilterDto } from '@core/infrastructure/filtering/dtos/base-filter.dto';
import { SecureFilterService } from '@core/infrastructure/filtering/services/secure-filter.service';
import { CreateOrderItemDto } from './dto/create-order-item.dto';
import { UpdateOrderItemDto } from './dto/update-order-item.dto';
import { OrderItem } from './domain/order-item';
import { OrderAssignmentService } from './services/order-assignment.service';
import { OrderStatus } from './domain/order.types';
import { PaginatedResult } from '@utils/query-creator/interfaces';
import { DraftOrderDto } from '@app/business/order/orders/dto/draft-order.dto';
import { DraftOrderResponseDto } from '@app/business/order/orders/dto/draft-order-response.dto';
import { PriceSetsService } from '@app/business/pricing/price-sets/price-sets.service';
import {
  toTenantTimezone,
  getDayOfWeek,
  getTimeString,
} from '@app/business/pricing/utils/date.utils';
import { AvailabilityType } from '@app/business/pricing/price-sets/domain/price-set.types';

@ApiTags('Business - Order')
@ApiBearerAuth()
@ApiCookieAuth('contact_session_token')
@Controller({
  path: '/orders',
  version: '1',
})
@UseGuards(JwtContactAuthGuard, ContactPermissionGuard)
@RequireContactPermission('orders')
@ApiForbiddenResponse({ description: 'Insufficient permissions' })
export class OrdersController {
  constructor(
    private readonly ordersService: OrdersService,
    private readonly orderAssignmentService: OrderAssignmentService,
    private readonly orderPaymentService: OrderPaymentService,
    private readonly contactsService: ContactsService,
    private readonly secureFilterService: SecureFilterService,
    private readonly priceSetsService: PriceSetsService,
  ) {}

  /**
   * Validate scheduled times and price set availability
   * @param scheduledCollectionTime Collection time
   * @param scheduledDeliveryTime Delivery time
   * @param priceSetId Optional price set ID to validate against
   */
  private async validateScheduledTimes(
    scheduledCollectionTime: Date | undefined,
    priceSetId?: string,
  ): Promise<Date | undefined> {
    if (!scheduledCollectionTime) {
      return undefined;
    }

    const collectionTime = new Date(scheduledCollectionTime);

    // Calculate delivery time (default: collection time + 4 hours)
    const deliveryTime = new Date(collectionTime);
    deliveryTime.setHours(deliveryTime.getHours() + 4);

    // Ensure collection time is in the future
    const now = new Date();
    if (collectionTime <= now) {
      throw new BadRequestException('Collection time must be in the future');
    }

    // If price set is provided, validate against price set schedule
    if (priceSetId) {
      try {
        // Convert to tenant timezone for consistent handling
        const tenantPickupDate = toTenantTimezone(collectionTime);
        const pickupDay = getDayOfWeek(tenantPickupDate);
        const pickupTime = getTimeString(tenantPickupDate);

        // Get the price set schedule
        const schedule =
          await this.priceSetsService.getScheduleDetails(priceSetId);

        // Check if the price set is available for the scheduled time
        let isAvailable = false;

        if (schedule.availabilityType === AvailabilityType.Always) {
          isAvailable = true;
        } else if (
          schedule.availabilityType === AvailabilityType.Weekly &&
          schedule.schedule
        ) {
          // Check if the pickup day and time are within the schedule
          for (const scheduleItem of schedule.schedule) {
            const days = scheduleItem.days.split(',');
            if (days.includes(pickupDay)) {
              if (
                pickupTime >= scheduleItem.startTime &&
                pickupTime <= scheduleItem.endTime
              ) {
                isAvailable = true;
                break;
              }
            }
          }
        }

        if (!isAvailable) {
          throw new BadRequestException(
            'The scheduled collection time is not available for the selected price set',
          );
        }
      } catch (error) {
        if (error instanceof BadRequestException) {
          throw error;
        }
        // If the price set is not found or there's another error, log it but don't block the order
        console.error(
          `Error validating schedule for price set ${priceSetId}:`,
          error,
        );
      }
    }

    // Return the calculated delivery time
    return deliveryTime;
  }

  @Post()
  @ApiOperation({ summary: 'Create a new order' })
  @ApiCreatedResponse({
    description: 'Order created successfully',
    type: OrderResponseDto,
  })
  @ApiForbiddenResponse({
    description: 'Insufficient permissions',
  })
  @ApiUnauthorizedResponse({ description: 'User not authenticated' })
  @ApiBody({ type: CreateOrderDto })
  async create(
    @CurrentUser() contactData: JwtPayload,
    @Body() createOrderDto: CreateOrderDto,
  ): Promise<OrderResponseDto> {
    const contact = await this.contactsService.findById(contactData.sub);
    if (!contact.tenantId) {
      throw new Error('Contact does not have a tenant ID');
    }

    // Extract customer information from the JWT token and contact data
    // The contact ID is the submittedById
    const submittedById = contactData.sub;
    // The requestedById is also the contact ID in this case
    const requestedById = contactData.sub;
    // The customerId is the userId of the contact
    const customerId = contact.userId;

    // Create a new order DTO with the extracted customer information
    const enhancedOrderDto = {
      ...createOrderDto,
      customerId,
      requestedById,
      submittedById,
    };

    // Validate scheduled times and calculate delivery time
    const calculatedDeliveryTime = await this.validateScheduledTimes(
      enhancedOrderDto.scheduledCollectionTime,
      enhancedOrderDto.priceSetId,
    );

    // Add the calculated delivery time to the enhanced order DTO
    if (calculatedDeliveryTime) {
      (enhancedOrderDto as any).scheduledDeliveryTime = calculatedDeliveryTime;
    }

    return this.ordersService.createOrder(
      contact.tenantId,
      contactData.sub,
      enhancedOrderDto,
    );
  }

  @Get()
  @ApiOperation({ summary: 'Get all orders for the current tenant' })
  @ApiOkResponse({
    description: 'List of orders with pagination info',
    type: FindAllOrdersResponseDto,
  })
  @ApiForbiddenResponse({
    description: 'Insufficient permissions',
  })
  async findAll(
    @CurrentUser() contactData: JwtPayload,
    @Req() request: Request,
    @Query() filter: BaseFilterDto,
  ) {
    const contact = await this.contactsService.findById(contactData.sub);
    if (!contact.tenantId) {
      throw new Error('Contact does not have a tenant ID');
    }

    const combinedFilter = this.secureFilterService.parseKeyOperatorValueQuery(
      request.query,
      filter,
    );

    return this.ordersService.findAll(contact.tenantId, combinedFilter);
  }

  @Get('tracking/:trackingNumber')
  @ApiOperation({ summary: 'Get an order by tracking number' })
  @ApiOkResponse({
    description: 'Order details with complete history',
    type: OrderDetailResponseDto,
  })
  @ApiNotFoundResponse({ description: 'Order not found' })
  @ApiForbiddenResponse({
    description: 'Insufficient permissions',
  })
  @ApiParam({
    name: 'trackingNumber',
    description: 'Order tracking number',
    type: 'string',
    example: 'TRK-20250408-AB12C',
  })
  async findByTrackingNumber(
    @CurrentUser() contactData: JwtPayload,
    @Param('trackingNumber') trackingNumber: string,
  ): Promise<OrderDetailResponseDto> {
    const contact = await this.contactsService.findById(contactData.sub);
    if (!contact.tenantId) {
      throw new Error('Contact does not have a tenant ID');
    }

    return this.ordersService.findByTrackingNumber(
      contact.tenantId,
      trackingNumber,
    );
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get an order by ID' })
  @ApiOkResponse({
    description: 'Order details with complete history',
    type: OrderDetailResponseDto,
  })
  @ApiNotFoundResponse({ description: 'Order not found' })
  @ApiForbiddenResponse({
    description: 'Insufficient permissions',
  })
  @ApiParam({
    name: 'id',
    description: 'Order ID',
    type: 'string',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  async findOne(
    @CurrentUser() contactData: JwtPayload,
    @Param('id') id: string,
  ): Promise<OrderDetailResponseDto> {
    const contact = await this.contactsService.findById(contactData.sub);
    if (!contact.tenantId) {
      throw new Error('Contact does not have a tenant ID');
    }

    return this.ordersService.findOne(contact.tenantId, id);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Soft delete an order' })
  @ApiNoContentResponse({
    description: 'Order has been successfully soft-deleted',
  })
  @ApiNotFoundResponse({ description: 'Order not found' })
  @ApiForbiddenResponse({
    description: 'Insufficient permissions',
  })
  @ApiParam({
    name: 'id',
    description: 'Order ID',
    type: 'string',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  @HttpCode(HttpStatus.NO_CONTENT)
  async softDelete(
    @CurrentUser() contactData: JwtPayload,
    @Param('id') id: string,
  ): Promise<void> {
    const contact = await this.contactsService.findById(contactData.sub);
    if (!contact.tenantId) {
      throw new Error('Contact does not have a tenant ID');
    }

    await this.ordersService.remove(contact.tenantId, id, contactData.sub);
  }

  @Post(':id/restore')
  @ApiOperation({ summary: 'Restore a soft-deleted order' })
  @ApiOkResponse({
    description: 'Order has been successfully restored',
    type: OrderResponseDto,
  })
  @ApiNotFoundResponse({ description: 'Order not found' })
  @ApiForbiddenResponse({
    description: 'Insufficient permissions',
  })
  @ApiParam({
    name: 'id',
    description: 'Order ID',
    type: 'string',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  async restore(
    @CurrentUser() contactData: JwtPayload,
    @Param('id') id: string,
  ): Promise<OrderResponseDto> {
    const contact = await this.contactsService.findById(contactData.sub);
    if (!contact.tenantId) {
      throw new Error('Contact does not have a tenant ID');
    }

    return this.ordersService.restore(contact.tenantId, id, contactData.sub);
  }

  @Delete(':id/permanent')
  @ApiOperation({
    summary: 'Permanently delete an order',
    description:
      'WARNING: This operation cannot be undone. The order will be permanently removed from the system.',
  })
  @ApiNoContentResponse({
    description: 'Order has been permanently deleted',
  })
  @ApiNotFoundResponse({ description: 'Order not found' })
  @ApiForbiddenResponse({
    description: 'Insufficient permissions',
  })
  @ApiParam({
    name: 'id',
    description: 'Order ID',
    type: 'string',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  @HttpCode(HttpStatus.NO_CONTENT)
  async hardDelete(
    @CurrentUser() contactData: JwtPayload,
    @Param('id') id: string,
  ): Promise<void> {
    const contact = await this.contactsService.findById(contactData.sub);
    if (!contact.tenantId) {
      throw new Error('Contact does not have a tenant ID');
    }

    await this.ordersService.hardDelete(contact.tenantId, id);
  }

  @Post(':id/items')
  @ApiOperation({ summary: 'Add an item to an order' })
  @ApiCreatedResponse({
    description: 'Item added successfully',
    schema: {
      type: 'object',
      properties: {
        id: { type: 'string', example: '550e8400-e29b-41d4-a716-446655440000' },
        orderId: {
          type: 'string',
          example: '550e8400-e29b-41d4-a716-446655441111',
        },
        // Other order item properties
      },
    },
  })
  @ApiNotFoundResponse({ description: 'Order not found' })
  @ApiForbiddenResponse({
    description: 'Insufficient permissions',
  })
  @ApiParam({
    name: 'id',
    description: 'Order ID',
    type: 'string',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  @ApiBody({ type: CreateOrderItemDto })
  async addOrderItem(
    @CurrentUser() contactData: JwtPayload,
    @Param('id') id: string,
    @Body() createOrderItemDto: CreateOrderItemDto,
  ): Promise<OrderItem> {
    const contact = await this.contactsService.findById(contactData.sub);
    if (!contact.tenantId) {
      throw new Error('Contact does not have a tenant ID');
    }

    return this.ordersService.addOrderItem(
      contact.tenantId,
      id,
      contactData.sub,
      createOrderItemDto,
    );
  }

  @Put(':id/items/:itemId')
  @ApiOperation({ summary: 'Update an order item' })
  @ApiOkResponse({
    description: 'Item updated successfully',
    schema: {
      type: 'object',
      properties: {
        id: { type: 'string', example: '550e8400-e29b-41d4-a716-446655440000' },
        orderId: {
          type: 'string',
          example: '550e8400-e29b-41d4-a716-446655441111',
        },
        // Other order item properties
      },
    },
  })
  @ApiNotFoundResponse({ description: 'Order or item not found' })
  @ApiForbiddenResponse({
    description: 'Insufficient permissions',
  })
  @ApiParam({
    name: 'id',
    description: 'Order ID',
    type: 'string',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  @ApiParam({
    name: 'itemId',
    description: 'Order Item ID',
    type: 'string',
    example: '550e8400-e29b-41d4-a716-446655442222',
  })
  @ApiBody({ type: UpdateOrderItemDto })
  async updateOrderItem(
    @CurrentUser() contactData: JwtPayload,
    @Param('id') id: string,
    @Param('itemId') itemId: string,
    @Body() updateOrderItemDto: UpdateOrderItemDto,
  ): Promise<OrderItem> {
    const contact = await this.contactsService.findById(contactData.sub);
    if (!contact.tenantId) {
      throw new Error('Contact does not have a tenant ID');
    }

    // Add itemId to the update DTO (to ensure it's consistent with URL)
    const updateDto: UpdateOrderItemDto = {
      ...updateOrderItemDto,
      id: itemId,
    };

    return this.ordersService.updateOrderItem(
      contact.tenantId,
      id,
      contactData.sub,
      updateDto,
    );
  }

  @Delete(':id/items/:itemId')
  @ApiOperation({ summary: 'Remove an item from an order' })
  @ApiNoContentResponse({
    description: 'Item has been successfully removed',
  })
  @ApiNotFoundResponse({ description: 'Order or item not found' })
  @ApiForbiddenResponse({
    description: 'Insufficient permissions',
  })
  @ApiParam({
    name: 'id',
    description: 'Order ID',
    type: 'string',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  @ApiParam({
    name: 'itemId',
    description: 'Order Item ID',
    type: 'string',
    example: '550e8400-e29b-41d4-a716-446655442222',
  })
  @HttpCode(HttpStatus.NO_CONTENT)
  async removeOrderItem(
    @CurrentUser() contactData: JwtPayload,
    @Param('id') id: string,
    @Param('itemId') itemId: string,
  ): Promise<void> {
    const contact = await this.contactsService.findById(contactData.sub);
    if (!contact.tenantId) {
      throw new Error('Contact does not have a tenant ID');
    }

    await this.ordersService.removeOrderItem(
      contact.tenantId,
      id,
      contactData.sub,
      itemId,
    );
  }

  @Post(':id/lock')
  @ApiOperation({
    summary: 'Lock an order to prevent concurrent modifications',
  })
  @ApiOkResponse({
    description: 'Order has been successfully locked',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
      },
    },
  })
  @ApiNotFoundResponse({ description: 'Order not found' })
  @ApiForbiddenResponse({
    description: 'Insufficient permissions or order already locked',
  })
  @ApiParam({
    name: 'id',
    description: 'Order ID',
    type: 'string',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        reason: {
          type: 'string',
          example: 'Editing order details',
        },
      },
    },
  })
  async lockOrder(
    @CurrentUser() contactData: JwtPayload,
    @Param('id') id: string,
    @Body() lockData: { reason?: string },
  ): Promise<{ success: boolean }> {
    const contact = await this.contactsService.findById(contactData.sub);
    if (!contact.tenantId) {
      throw new Error('Contact does not have a tenant ID');
    }

    const result = await this.ordersService.lockOrder(
      contact.tenantId,
      id,
      contactData.sub,
      lockData.reason,
    );

    return { success: result };
  }

  @Post(':id/unlock')
  @ApiOperation({ summary: 'Unlock a locked order' })
  @ApiOkResponse({
    description: 'Order has been successfully unlocked',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
      },
    },
  })
  @ApiNotFoundResponse({ description: 'Order not found' })
  @ApiForbiddenResponse({
    description: 'Insufficient permissions or order locked by another user',
  })
  @ApiParam({
    name: 'id',
    description: 'Order ID',
    type: 'string',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  async unlockOrder(
    @CurrentUser() contactData: JwtPayload,
    @Param('id') id: string,
  ): Promise<{ success: boolean }> {
    const contact = await this.contactsService.findById(contactData.sub);
    if (!contact.tenantId) {
      throw new Error('Contact does not have a tenant ID');
    }

    const result = await this.ordersService.unlockOrder(
      contact.tenantId,
      id,
      contactData.sub,
    );

    return { success: result };
  }

  @Get(':id/status-history')
  @ApiOperation({ summary: 'Get order status history' })
  @ApiOkResponse({
    description: 'Order status history',
    schema: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          id: { type: 'string' },
          orderId: { type: 'string' },
          previousStatus: { type: 'string' },
          newStatus: { type: 'string' },
          reason: { type: 'string' },
          comments: { type: 'string' },
          changedBy: { type: 'string' },
          changedAt: { type: 'string', format: 'date-time' },
        },
      },
    },
  })
  @ApiNotFoundResponse({ description: 'Order not found' })
  @ApiForbiddenResponse({
    description: 'Insufficient permissions',
  })
  @ApiParam({
    name: 'id',
    description: 'Order ID',
    type: 'string',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  async getStatusHistory(
    @CurrentUser() contactData: JwtPayload,
    @Param('id') id: string,
  ): Promise<any[]> {
    const contact = await this.contactsService.findById(contactData.sub);
    if (!contact.tenantId) {
      throw new Error('Contact does not have a tenant ID');
    }

    return this.ordersService.getOrderStatusHistory(contact.tenantId, id);
  }

  @Get(':id/assignment-history')
  @ApiOperation({ summary: 'Get order assignment history' })
  @ApiOkResponse({
    description: 'Order assignment history',
    schema: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          id: { type: 'string' },
          orderId: { type: 'string' },
          previousAssigneeId: { type: 'string' },
          newAssigneeId: { type: 'string' },
          assignedById: { type: 'string' },
          assignedVehicleId: { type: 'string' },
          previousVehicleId: { type: 'string' },
          assignmentType: { type: 'string' },
          reason: { type: 'string' },
          createdAt: { type: 'string', format: 'date-time' },
        },
      },
    },
  })
  @ApiNotFoundResponse({ description: 'Order not found' })
  @ApiForbiddenResponse({
    description: 'Insufficient permissions',
  })
  @ApiParam({
    name: 'id',
    description: 'Order ID',
    type: 'string',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  async getAssignmentHistory(
    @CurrentUser() contactData: JwtPayload,
    @Param('id') id: string,
  ): Promise<any[]> {
    const contact = await this.contactsService.findById(contactData.sub);
    if (!contact.tenantId) {
      throw new Error('Contact does not have a tenant ID');
    }

    return this.ordersService.getOrderAssignmentHistory(contact.tenantId, id);
  }

  @Get(':id/stop-history')
  @ApiOperation({ summary: 'Get order stop history' })
  @ApiOkResponse({
    description: 'Order stop history',
    schema: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          id: { type: 'string' },
          orderId: { type: 'string' },
          stopType: { type: 'string' },
          scheduledTime: { type: 'string', format: 'date-time' },
          actualTime: { type: 'string', format: 'date-time' },
          success: { type: 'boolean' },
          failureReason: { type: 'string' },
          notes: { type: 'string' },
          createdBy: { type: 'string' },
          createdAt: { type: 'string', format: 'date-time' },
        },
      },
    },
  })
  @ApiNotFoundResponse({ description: 'Order not found' })
  @ApiForbiddenResponse({
    description: 'Insufficient permissions',
  })
  @ApiParam({
    name: 'id',
    description: 'Order ID',
    type: 'string',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  async getStopHistory(
    @CurrentUser() contactData: JwtPayload,
    @Param('id') id: string,
  ): Promise<any[]> {
    const contact = await this.contactsService.findById(contactData.sub);
    if (!contact.tenantId) {
      throw new Error('Contact does not have a tenant ID');
    }

    return this.ordersService.getOrderStopHistory(contact.tenantId, id);
  }

  @Post(':id/payments')
  @ApiOperation({ summary: 'Create a payment for an order' })
  @ApiOkResponse({
    description: 'Payment created successfully',
    schema: {
      type: 'object',
    },
  })
  @ApiNotFoundResponse({ description: 'Order not found' })
  @ApiForbiddenResponse({
    description: 'Insufficient permissions',
  })
  @ApiParam({
    name: 'id',
    description: 'Order ID',
    type: 'string',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  @ApiBody({ type: CreateOrderPaymentDto })
  async createOrderPayment(
    @CurrentUser() contactData: JwtPayload,
    @Param('id') id: string,
    @Body() createOrderPaymentDto: CreateOrderPaymentDto,
  ): Promise<any> {
    const contact = await this.contactsService.findById(contactData.sub);
    if (!contact.tenantId) {
      throw new Error('Contact does not have a tenant ID');
    }

    // Verify order belongs to tenant
    await this.ordersService.findOne(contact.tenantId, id);

    return this.orderPaymentService.createOrderPayment(
      id,
      contactData.sub,
      createOrderPaymentDto.paymentMethodId,
      createOrderPaymentDto.description,
      createOrderPaymentDto.metadata,
      createOrderPaymentDto.triggerType,
    );
  }

  @Get(':id/payments')
  @ApiOperation({ summary: 'Get payments for an order' })
  @ApiOkResponse({
    description: 'List of payments for the order',
    schema: {
      type: 'array',
    },
  })
  @ApiNotFoundResponse({ description: 'Order not found' })
  @ApiForbiddenResponse({
    description: 'Insufficient permissions',
  })
  @ApiParam({
    name: 'id',
    description: 'Order ID',
    type: 'string',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  async getOrderPayments(
    @CurrentUser() contactData: JwtPayload,
    @Param('id') id: string,
  ): Promise<any[]> {
    const contact = await this.contactsService.findById(contactData.sub);
    if (!contact.tenantId) {
      throw new Error('Contact does not have a tenant ID');
    }

    // Verify order belongs to tenant
    await this.ordersService.findOne(contact.tenantId, id);

    return this.orderPaymentService.getOrderPayments(id);
  }

  @Post(':id/payments/:paymentId/process')
  @ApiOperation({ summary: 'Process a payment for an order' })
  @ApiOkResponse({
    description: 'Payment processed successfully',
    schema: {
      type: 'object',
    },
  })
  @ApiNotFoundResponse({ description: 'Order or payment not found' })
  @ApiForbiddenResponse({
    description: 'Insufficient permissions',
  })
  @ApiParam({
    name: 'id',
    description: 'Order ID',
    type: 'string',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  @ApiParam({
    name: 'paymentId',
    description: 'Payment ID',
    type: 'string',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  async processOrderPayment(
    @CurrentUser() contactData: JwtPayload,
    @Param('id') id: string,
    @Param('paymentId') paymentId: string,
  ): Promise<any> {
    const contact = await this.contactsService.findById(contactData.sub);
    if (!contact.tenantId) {
      throw new Error('Contact does not have a tenant ID');
    }

    // Verify order belongs to tenant
    await this.ordersService.findOne(contact.tenantId, id);

    return this.orderPaymentService.processOrderPayment(
      id,
      paymentId,
      contactData.sub,
    );
  }

  @Post(':id/assign')
  @ApiOperation({ summary: 'Assign an order to a driver and vehicle' })
  @ApiOkResponse({
    description: 'Order has been successfully assigned',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
      },
    },
  })
  @ApiNotFoundResponse({ description: 'Order not found' })
  @ApiForbiddenResponse({
    description: 'Insufficient permissions',
  })
  @ApiParam({
    name: 'id',
    description: 'Order ID',
    type: 'string',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  @ApiBody({
    schema: {
      type: 'object',
      required: ['driverId'],
      properties: {
        driverId: {
          type: 'string',
          example: '550e8400-e29b-41d4-a716-446655443333',
        },
        vehicleId: {
          type: 'string',
          example: '550e8400-e29b-41d4-a716-446655444444',
        },
        reason: {
          type: 'string',
          example: 'Most appropriate driver for this delivery',
        },
      },
    },
  })
  async assignOrder(
    @CurrentUser() contactData: JwtPayload,
    @Param('id') id: string,
    @Body()
    assignData: { driverId: string; vehicleId?: string; reason?: string },
  ): Promise<{ success: boolean }> {
    const contact = await this.contactsService.findById(contactData.sub);
    if (!contact.tenantId) {
      throw new Error('Contact does not have a tenant ID');
    }

    // First validate that the order exists and belongs to the tenant
    await this.ordersService.findOne(contact.tenantId, id);

    const result = await this.orderAssignmentService.assignOrder(
      id,
      assignData.driverId,
      contactData.sub,
      assignData.vehicleId,
      assignData.reason,
    );

    return { success: result };
  }

  @Post(':id/unassign')
  @ApiOperation({
    summary: 'Unassign an order from the current driver/vehicle',
  })
  @ApiOkResponse({
    description: 'Order has been successfully unassigned',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
      },
    },
  })
  @ApiNotFoundResponse({ description: 'Order not found' })
  @ApiForbiddenResponse({
    description: 'Insufficient permissions',
  })
  @ApiParam({
    name: 'id',
    description: 'Order ID',
    type: 'string',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        reason: {
          type: 'string',
          example: 'Driver no longer available',
        },
      },
    },
  })
  async unassignOrder(
    @CurrentUser() contactData: JwtPayload,
    @Param('id') id: string,
    @Body() unassignData: { reason?: string },
  ): Promise<{ success: boolean }> {
    const contact = await this.contactsService.findById(contactData.sub);
    if (!contact.tenantId) {
      throw new Error('Contact does not have a tenant ID');
    }

    // First validate that the order exists and belongs to the tenant
    await this.ordersService.findOne(contact.tenantId, id);

    const result = await this.orderAssignmentService.unassignOrder(
      id,
      contactData.sub,
      unassignData.reason,
    );

    return { success: result };
  }

  @Post(':id/reassign')
  @ApiOperation({ summary: 'Reassign an order to a different driver/vehicle' })
  @ApiOkResponse({
    description: 'Order has been successfully reassigned',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
      },
    },
  })
  @ApiNotFoundResponse({ description: 'Order not found' })
  @ApiForbiddenResponse({
    description: 'Insufficient permissions',
  })
  @ApiParam({
    name: 'id',
    description: 'Order ID',
    type: 'string',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  @ApiBody({
    schema: {
      type: 'object',
      required: ['driverId'],
      properties: {
        driverId: {
          type: 'string',
          example: '550e8400-e29b-41d4-a716-446655443333',
        },
        vehicleId: {
          type: 'string',
          example: '550e8400-e29b-41d4-a716-446655444444',
        },
        reason: {
          type: 'string',
          example: 'Previous driver unavailable',
        },
      },
    },
  })
  async reassignOrder(
    @CurrentUser() contactData: JwtPayload,
    @Param('id') id: string,
    @Body()
    reassignData: { driverId: string; vehicleId?: string; reason?: string },
  ): Promise<{ success: boolean }> {
    const contact = await this.contactsService.findById(contactData.sub);
    if (!contact.tenantId) {
      throw new Error('Contact does not have a tenant ID');
    }

    // First validate that the order exists and belongs to the tenant
    await this.ordersService.findOne(contact.tenantId, id);

    const result = await this.orderAssignmentService.reassignOrder(
      id,
      reassignData.driverId,
      contactData.sub,
      reassignData.vehicleId,
      reassignData.reason,
    );

    return { success: result };
  }

  @Post('draft')
  @ApiOperation({
    summary: 'Create a draft order with minimal information',
    description:
      'Creates a draft order that only requires pickup and delivery locations. The order can be completed later by adding package details, timing, and pricing information.',
  })
  @ApiCreatedResponse({
    description: 'Draft order created successfully',
    type: DraftOrderResponseDto,
  })
  @ApiForbiddenResponse({
    description: 'Insufficient permissions',
  })
  @ApiUnauthorizedResponse({ description: 'User not authenticated' })
  @ApiBody({
    type: DraftOrderDto,
    description: 'Draft order information with pickup and delivery locations',
  })
  async createDraft(
    @CurrentUser() contactData: JwtPayload,
    @Body() draftOrderDto: DraftOrderDto,
  ): Promise<DraftOrderResponseDto> {
    const contact = await this.contactsService.findById(contactData.sub);
    const customerId = contact.userId;
    draftOrderDto.customerId = customerId;
    if (!contact.tenantId) {
      throw new Error('Contact does not have a tenant ID');
    }
    console.log('Draft Order DTO:', draftOrderDto);

    return this.ordersService.createDraftOrder(
      contact.tenantId,
      contactData.sub,
      draftOrderDto,
    );
  }

  @Put(':id/complete-draft')
  @ApiOperation({
    summary: 'Complete a draft order',
    description:
      'Converts a draft order to a full order by adding all required information like package details, timing, and pricing.',
  })
  @ApiOkResponse({
    description: 'Draft order completed successfully',
    type: OrderResponseDto,
  })
  @ApiNotFoundResponse({ description: 'Draft order not found' })
  @ApiForbiddenResponse({
    description: 'Insufficient permissions or order is not a draft',
  })
  @ApiParam({
    name: 'id',
    description: 'Draft Order ID',
    type: 'string',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  @ApiBody({
    type: CreateOrderDto,
    description: 'Complete order information to finalize the draft',
  })
  async completeDraft(
    @CurrentUser() contactData: JwtPayload,
    @Param('id') id: string,
    @Body() completeOrderDto: CreateOrderDto,
  ): Promise<OrderResponseDto> {
    const contact = await this.contactsService.findById(contactData.sub);
    if (!contact.tenantId) {
      throw new Error('Contact does not have a tenant ID');
    }

    // Validate scheduled times and calculate delivery time
    const calculatedDeliveryTime = await this.validateScheduledTimes(
      completeOrderDto.scheduledCollectionTime,
      completeOrderDto.priceSetId,
    );

    // Add the calculated delivery time to the complete order DTO
    if (calculatedDeliveryTime) {
      (completeOrderDto as any).scheduledDeliveryTime = calculatedDeliveryTime;
    }

    return this.ordersService.completeDraftOrder(
      contact.tenantId,
      id,
      contactData.sub,
      completeOrderDto,
    );
  }

  @Get('drafts')
  @ApiOperation({
    summary: 'Get all draft orders for the current tenant',
    description:
      'Retrieves all orders in draft status that are awaiting completion.',
  })
  @ApiOkResponse({
    description: 'List of draft orders with pagination info',
    type: FindAllOrdersResponseDto,
  })
  @ApiForbiddenResponse({
    description: 'Insufficient permissions',
  })
  async findAllDrafts(
    @CurrentUser() contactData: JwtPayload,
    @Req() request: Request,
    @Query() filter: BaseFilterDto,
  ): Promise<PaginatedResult<OrderResponseDto>> {
    const contact = await this.contactsService.findById(contactData.sub);
    if (!contact.tenantId) {
      throw new Error('Contact does not have a tenant ID');
    }

    const draftFilter = {
      ...filter,
      filters: [
        {
          field: 'status',
          operator: 'eq',
          value: OrderStatus.Draft,
        },
      ],
    };

    const combinedFilter = this.secureFilterService.parseKeyOperatorValueQuery(
      request.query,
      draftFilter,
    );

    return this.ordersService.findAll(contact.tenantId, combinedFilter);
  }
}
