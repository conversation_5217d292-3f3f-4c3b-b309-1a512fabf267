import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { FileUploadEntity } from '../entities/file-upload.entity';
import { FileUpload } from '../../domain/file-upload';
import { FileUploadStatus } from '../../domain/file-upload.types';

@Injectable()
export class FileUploadRepository {
  constructor(
    @InjectRepository(FileUploadEntity)
    private readonly fileUploadRepository: Repository<FileUploadEntity>,
  ) {}

  async create(fileUpload: FileUpload): Promise<FileUpload> {
    const entity = this.toEntity(fileUpload);
    const savedEntity = await this.fileUploadRepository.save(entity);
    return this.toDomain(savedEntity);
  }

  async findById(id: string): Promise<FileUpload | null> {
    const entity = await this.fileUploadRepository.findOne({
      where: { id },
    });
    return entity ? this.toDomain(entity) : null;
  }

  async findByEntityTypeAndId(
    entityType: string,
    entityId: string,
  ): Promise<FileUpload[]> {
    const entities = await this.fileUploadRepository.find({
      where: {
        entityType,
        entityId,
        status: FileUploadStatus.Active,
      },
    });
    return entities.map((entity) => this.toDomain(entity));
  }

  async update(fileUpload: FileUpload): Promise<FileUpload> {
    const entity = this.toEntity(fileUpload);
    const savedEntity = await this.fileUploadRepository.save(entity);
    return this.toDomain(savedEntity);
  }

  async softDelete(id: string, updatedBy: string | null): Promise<void> {
    const updateData: any = {
      status: FileUploadStatus.Deleted,
      updatedAt: new Date(),
    };
    
    if (updatedBy !== null) {
      updateData.updatedBy = updatedBy;
    }
    
    await this.fileUploadRepository.update({ id }, updateData);
  }

  async hardDelete(id: string): Promise<void> {
    await this.fileUploadRepository.delete(id);
  }

  private toEntity(domain: FileUpload): FileUploadEntity {
    const entity = new FileUploadEntity();
    entity.id = domain.id;
    entity.filename = domain.filename;
    entity.originalFilename = domain.originalFilename;
    entity.type = domain.type;
    entity.mimeType = domain.mimeType;
    entity.size = domain.size;
    entity.path = domain.path;
    entity.url = domain.url || '';
    entity.metadata = domain.metadata || {};
    entity.entityType = domain.entityType;
    entity.entityId = domain.entityId;
    entity.createdAt = domain.createdAt;
    entity.updatedAt = domain.updatedAt;
    entity.createdBy = domain.createdBy || null;
    entity.updatedBy = domain.updatedBy || null;
    entity.status = domain.status;
    return entity;
  }

  private toDomain(entity: FileUploadEntity): FileUpload {
    const domain = new FileUpload();
    domain.id = entity.id;
    domain.filename = entity.filename;
    domain.originalFilename = entity.originalFilename;
    domain.type = entity.type;
    domain.mimeType = entity.mimeType;
    domain.size = entity.size;
    domain.path = entity.path;
    domain.url = entity.url ?? undefined;
    domain.metadata = entity.metadata ?? undefined;
    domain.entityType = entity.entityType;
    domain.entityId = entity.entityId;
    domain.createdAt = entity.createdAt;
    domain.updatedAt = entity.updatedAt;
    domain.createdBy = entity.createdBy;
    domain.updatedBy = entity.updatedBy ?? undefined;
    domain.status = entity.status;
    return domain;
  }
}
